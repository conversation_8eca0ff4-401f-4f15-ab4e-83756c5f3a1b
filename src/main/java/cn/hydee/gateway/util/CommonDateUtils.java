package com.yxt.order.common;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年06月18日 10:59
 * @email: <EMAIL>
 */
@Slf4j
public class CommonDateUtils {

  private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
  private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(
      DEFAULT_DATE_FORMAT);

  /**
   * 返回年月列表
   *
   * @param start
   * @return
   */
  public static List<String> dateYYYYMMListToNow(Date start) {
    LocalDate startDate = start.toInstant()
        .atZone(ZoneId.systemDefault())
        .toLocalDate();
    LocalDate currentDate = LocalDate.now();

    if (startDate.isAfter(currentDate)) {
      throw new RuntimeException("起始日期不能超过当前日期");
    }

    return getYearMonthList(startDate, currentDate);
  }


  public static List<String> getYearMonthList(LocalDate startDate, LocalDate currentDate) {
    List<String> yearMonthList = new ArrayList<>();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");

    YearMonth startYearMonth = YearMonth.from(startDate);
    YearMonth endYearMonth = YearMonth.from(currentDate);

    YearMonth currentYearMonth = startYearMonth;
    while (!currentYearMonth.isAfter(endYearMonth)) {
      yearMonthList.add(currentYearMonth.format(formatter));
      currentYearMonth = currentYearMonth.plusMonths(1);
    }

    return yearMonthList;
  }

  public static String yyyyMMNext(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);

    int year = calendar.get(Calendar.YEAR);
    int month = calendar.get(Calendar.MONTH) + 2;
    return String.format("%04d%02d", year, month);
  }

  public static String yyMMNext(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    // 移动到下一个月
    calendar.add(Calendar.MONTH, 1);
    // 获取年份的后两位
    int year = calendar.get(Calendar.YEAR) % 100;

    // 获取月份（1-12）
    int month = calendar.get(Calendar.MONTH) + 1;

    return String.format("%02d%02d", year, month);
  }

  public static String yyMMCurrent(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);

    int year = calendar.get(Calendar.YEAR) % 100;
    int month = calendar.get(Calendar.MONTH) + 1;
    return String.format("%02d%02d", year, month);
  }


  public static String yyyyMMCurrent(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);

    int year = calendar.get(Calendar.YEAR);
    int month = calendar.get(Calendar.MONTH) + 1;
    return String.format("%04d%02d", year, month);
  }


  public static Date addDay(Date date, int amount) {
    Calendar c = Calendar.getInstance(Locale.CHINA);
    c.setTime(date);
    c.add(5, amount);
    return c.getTime();
  }

  /**
   * 计算指定日期与当前日期相差的小时数
   *
   * @param date 指定的日期
   * @return 相差的小时数（向下取整）
   */
  public static long getHoursDifference(Date date) {
    if (date == null) {
      throw new IllegalArgumentException("Date cannot be null");
    }

    Date currentDate = new Date();
    long diffInMillies = Math.abs(currentDate.getTime() - date.getTime());
    return TimeUnit.HOURS.convert(diffInMillies, TimeUnit.MILLISECONDS);
  }

  /**
   * 计算指定日期与当前日期相差的小时数（包含小数部分）
   *
   * @param date 指定的日期
   * @return 相差的小时数（包含小数部分）
   */
  public static double getHoursDifferenceWithDecimal(Date date) {
    if (date == null) {
      throw new IllegalArgumentException("Date cannot be null");
    }

    Date currentDate = new Date();
    long diffInMillies = Math.abs(currentDate.getTime() - date.getTime());
    return diffInMillies / (1000.0 * 60 * 60);
  }

  public static String formatDate(Date date) {
    try {
      String dateFormat = "yyyy-MM-dd HH:mm:ss";
      SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
      return sdf.format(date);
    } catch (Exception e) {
      log.error("日期格式化出错", e);
      return date.toString();
    }
  }

  /**
   * 获取当前时间前N分钟的时间字符串
   *
   * @param minutes 前N分钟
   * @return 格式化的时间字符串 (yyyy-MM-dd HH:mm:ss)
   */
  public static String getMinutesAgo(int minutes) {
    LocalDateTime now = LocalDateTime.now();
    LocalDateTime minutesAgo = now.minusMinutes(minutes);
    return minutesAgo.format(formatter);
  }


  public static void main(String[] args) throws ParseException {
    System.out.println(getMinutesAgo(1));
  }

  /**
   * @param dateString
   * @return
   */
  //    String dateString = "2024-06-18 23:00:01";
  public static Date convert2Date(String dateString) {
    try {
      String dateFormat = "yyyy-MM-dd HH:mm:ss";
      SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
      // 设置时区为 GMT+8（北京时间）
      sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
      return sdf.parse(dateString);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }


  public static long calculateDaysBetween(Date startDate, Date endDate) {
    // 将 Date 转换为 Instant，然后转换为 LocalDate
    java.time.LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    java.time.LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

    // 计算两个日期之间的天数
    return ChronoUnit.DAYS.between(start, end);
  }
}
