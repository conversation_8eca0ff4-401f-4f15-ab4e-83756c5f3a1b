package cn.hydee.gateway.config;

import java.util.Date;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

/**
 * 响应式redis项配置
 */
@Component
public class ReactiveRedisConfig {

  /**
   * 异步非阻塞的RedisTemplate
   *
   * @param reactiveRedisConnectionFactory
   * @return
   */
  @Bean
  @Primary
  public ReactiveRedisTemplate<String, String> reactiveRedisTemplate(
      ReactiveRedisConnectionFactory reactiveRedisConnectionFactory) {
    StringRedisSerializer serializer = new StringRedisSerializer();
    RedisSerializationContext<String, String> context = RedisSerializationContext.<String, String>newSerializationContext()
        .key(serializer)
        .value(serializer)  // 使用字符串序列化器
        .hashKey(serializer)
        .hashValue(serializer)
        .build();
    return new ReactiveRedisTemplate<>(reactiveRedisConnectionFactory, context);
  }

  @Bean
  public ReactiveRedisTemplate<String, Date> reactiveRedisTemplateDate(
      ReactiveRedisConnectionFactory reactiveRedisConnectionFactory) {
    RedisSerializationContext<String, Date> context = RedisSerializationContext.<String, Date>newSerializationContext()
        .build();
    return new ReactiveRedisTemplate<>(reactiveRedisConnectionFactory, context);
  }

}