package cn.hydee.gateway.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 返回的基类
 * <AUTHOR>
 * @version 1.0
 * @date 2019/7/18 14:18
 */
@Getter
@Setter
public class ResponseBase<T> {
    private final static String SUCCESS_CODE = "10000";

    private String code;
    private String msg;
    private T data;

    public boolean checkSuccess() {
        return SUCCESS_CODE.equals(code);
    }

}
