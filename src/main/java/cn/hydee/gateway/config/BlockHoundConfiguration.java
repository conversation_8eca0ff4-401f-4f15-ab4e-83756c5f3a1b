package cn.hydee.gateway.config;

import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.BlockHound;
import reactor.blockhound.integration.BlockHoundIntegration;

/**
 * BlockHound配置集成
 *
 * 为Spring Cloud Gateway + Sentinel环境配置BlockHound允许列表
 * 解决响应式环境中的阻塞调用检测问题
 *
 * <AUTHOR> (moatkon)
 * @date 2025-06-03
 */
@Slf4j
public class BlockHoundConfiguration implements BlockHoundIntegration {

    @Override
    public void applyTo(BlockHound.Builder builder) {
        log.info("应用BlockHound配置 - 为Sentinel和网关环境配置允许列表");

        // === Sentinel日志相关的阻塞调用 ===
        // 文件输出流相关
        builder.allowBlockingCallsInside("java.io.FileOutputStream", "writeBytes");
        builder.allowBlockingCallsInside("java.io.FileOutputStream", "write");
        builder.allowBlockingCallsInside("java.io.BufferedOutputStream", "flushBuffer");
        builder.allowBlockingCallsInside("java.io.BufferedOutputStream", "flush");

        // JUL日志处理器相关
        builder.allowBlockingCallsInside("java.util.logging.FileHandler$MeteredStream", "flush");
        builder.allowBlockingCallsInside("java.util.logging.StreamHandler", "flush");
        builder.allowBlockingCallsInside("java.util.logging.FileHandler", "publish");
        builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.log.jul.DateFileLogHandler", "publish");

        // JUL日志系统核心
        builder.allowBlockingCallsInside("java.util.logging.Logger", "log");
        builder.allowBlockingCallsInside("java.util.logging.Logger", "doLog");
        builder.allowBlockingCallsInside("java.util.logging.Logger", "logp");

        // Sentinel日志适配器
        builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.log.jul.BaseJulLogger", "log");
        builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.log.jul.JavaLoggingAdapter", "info");
        builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.log.jul.JavaLoggingAdapter", "warn");
        builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.log.jul.JavaLoggingAdapter", "error");
        builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.log.RecordLog", "info");
        builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.log.RecordLog", "warn");

        // === Sentinel核心组件相关 ===
        // 节点管理
        builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.node.DefaultNode", "addChild");
        builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.node.DefaultNode", "removeChild");

        // 上下文管理
        builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.context.ContextUtil", "initDefaultContext");
        builder.allowBlockingCallsInside("com.alibaba.csp.sentinel.context.ContextUtil", "enter");

        // === 其他可能的阻塞调用 ===
        // Jedis客户端（同步Redis客户端）
        builder.allowBlockingCallsInside("redis.clients.jedis.Connection", "sendCommand");
        builder.allowBlockingCallsInside("redis.clients.jedis.BinaryJedis", "get");
        builder.allowBlockingCallsInside("redis.clients.jedis.BinaryJedis", "set");

        // Lettuce客户端（响应式Redis客户端）
        // 集群连接相关
        builder.allowBlockingCallsInside("io.lettuce.core.cluster.RedisClusterClient", "getPartitions");
        builder.allowBlockingCallsInside("io.lettuce.core.cluster.RedisClusterClient", "get");
        builder.allowBlockingCallsInside("java.util.concurrent.CompletableFuture", "get");
        builder.allowBlockingCallsInside("java.util.concurrent.CompletableFuture", "waitingGet");
        builder.allowBlockingCallsInside("java.util.concurrent.ForkJoinPool", "managedBlock");
        builder.allowBlockingCallsInside("java.util.concurrent.CompletableFuture$Signaller", "block");

        // Lettuce连接池和连接管理
        builder.allowBlockingCallsInside("io.lettuce.core.AbstractRedisClient", "connectStandalone");
        builder.allowBlockingCallsInside("io.lettuce.core.AbstractRedisClient", "getConnection");
        builder.allowBlockingCallsInside("io.lettuce.core.ConnectionFuture", "get");

        // Spring Data Redis Lettuce集成
        builder.allowBlockingCallsInside("org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory", "getConnection");
        builder.allowBlockingCallsInside("org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory", "getReactiveConnection");
        builder.allowBlockingCallsInside("org.springframework.data.redis.connection.lettuce.ClusterConnectionProvider", "getConnectionAsync");

        // 网络I/O相关
        builder.allowBlockingCallsInside("java.net.Socket", "connect");
        builder.allowBlockingCallsInside("java.net.SocketInputStream", "read");
        builder.allowBlockingCallsInside("java.net.SocketOutputStream", "write");

        // 类加载相关
        builder.allowBlockingCallsInside("java.lang.ClassLoader", "loadClass");
        builder.allowBlockingCallsInside("java.lang.Class", "forName");

        log.info("BlockHound配置应用完成 - 已配置{}个允许阻塞调用的规则", 20);
    }
}
