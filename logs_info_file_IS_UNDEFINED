2025-06-04 10:24:24.406|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 10:24:24.426|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 18740 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 10:24:24.426|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 10:24:25.796|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 10:24:25.817|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 10:24:25.853|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-04 10:24:26.044|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 10:24:26.046|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 10:24:26.418|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=32bd2808-22cc-3a45-b749-6867309bdb63
2025-06-04 10:24:26.502|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 10:24:26.506|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 10:24:34.760|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:24:38.795|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:24:42.835|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:24:46.867|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:24:46.868|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 10:24:50.913|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:24:54.963|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:24:54.963|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 10:24:55.014|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 10:24:55.443|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 10:24:55.448|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 10:24:55.477|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 10:24:55.599|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$ec30adfe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 10:24:55.620|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 10:24:55.621|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 10:24:55.623|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 10:24:59.003|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:25:03.024|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:25:07.026|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:25:11.029|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:25:15.032|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:25:15.071|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 10:25:16.615|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-04 10:25:16.615|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-04 10:25:16.624|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-04 10:25:16.624|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-04 10:25:16.624|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-04 10:25:16.625|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-04 10:25:16.625|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-04 10:25:16.625|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-04 10:25:16.625|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-04 10:25:16.625|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-04 10:25:16.625|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-04 10:25:16.626|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-04 10:25:16.626|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-04 10:25:16.861|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 10:25:16.942|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 10:25:17.019|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 10:25:17.088|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 10:25:17.133|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 10:25:17.162|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 10:25:17.383|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 10:25:17.383|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 10:25:17.393|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 10:25:17.394|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 10:25:17.880|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-04 10:25:17.883|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-04 10:25:18.077|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-04 10:25:18.254|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-04 10:25:18.385|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-04 10:25:18.934|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-04 10:25:19.033|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:25:19.142|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-04 10:25:19.706|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-04 10:25:20.042|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.868 seconds (JVM running for 59.674)
2025-06-04 10:25:20.044|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-04 10:25:20.044|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-04 10:25:20.048|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-04 10:25:20.119|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-04 10:25:39.036|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:26:15.042|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:27:23.044|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:29:27.046|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:29:38.770|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:30:07.041|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:31:31.049|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:33:35.053|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:34:38.765|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:35:07.041|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:35:39.055|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:37:43.058|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:39:38.767|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:39:47.061|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:40:07.028|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:41:51.064|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 10:43:55.066|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:19:33.748|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 14:19:33.777|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 25852 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 14:19:33.778|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 14:19:35.309|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 14:19:35.315|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 14:19:35.381|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 41 ms. Found 0 Redis repository interfaces.
2025-06-04 14:19:35.641|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 14:19:35.650|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 14:19:36.124|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=14cb4af2-f73b-3450-836f-f0513b59db73
2025-06-04 14:19:36.232|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 14:19:36.236|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 14:19:44.514|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:19:48.546|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:19:52.598|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:19:56.647|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:19:56.648|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 14:20:00.695|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:20:04.746|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:20:04.746|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 14:20:04.814|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 14:20:05.323|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:20:05.328|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:20:05.366|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:20:05.529|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$ebf6e42] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:20:05.560|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:20:05.561|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:20:05.566|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:20:08.777|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:20:12.788|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:20:16.790|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:20:20.792|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:20:24.794|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:20:24.828|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 14:20:26.337|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-04 14:20:26.338|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-04 14:20:26.338|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-04 14:20:26.338|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-04 14:20:26.338|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-04 14:20:26.338|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-04 14:20:26.339|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-04 14:20:26.339|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-04 14:20:26.339|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-04 14:20:26.339|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-04 14:20:26.339|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-04 14:20:26.339|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-04 14:20:26.340|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-04 14:20:26.544|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 14:20:26.623|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 14:20:26.696|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 14:20:26.754|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 14:20:26.799|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 14:20:26.825|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 14:20:27.031|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 14:20:27.031|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 14:20:27.040|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 14:20:27.040|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 14:20:27.507|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-04 14:20:27.510|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-04 14:20:27.692|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-04 14:20:27.886|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-04 14:20:28.017|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-04 14:20:28.782|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-04 14:20:28.795|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:20:28.989|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-04 14:20:29.581|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-04 14:20:29.873|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 57.454 seconds (JVM running for 60.473)
2025-06-04 14:20:29.875|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-04 14:20:29.875|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-04 14:20:29.879|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-04 14:20:29.939|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-04 14:20:48.798|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:21:03.729|WARN| N/A||reactor-http-nio-2|c.h.g.filter.AccessGatewayFilter:289|解析JWT用户信息异常
cn.hydee.gateway.exception.UserTokenException: User token expired!
	at cn.hydee.gateway.util.UserAuthUtil.getInfoFromToken(UserAuthUtil.java:22)
	at cn.hydee.gateway.filter.AccessGatewayFilter.getJWTUserReactive(AccessGatewayFilter.java:261)
	at cn.hydee.gateway.filter.AccessGatewayFilter.filter(AccessGatewayFilter.java:123)
	at org.springframework.cloud.gateway.handler.FilteringWebHandler$GatewayFilterAdapter.filter(FilteringWebHandler.java:137)
	at org.springframework.cloud.gateway.filter.OrderedGatewayFilter.filter(OrderedGatewayFilter.java:44)
	at org.springframework.cloud.gateway.handler.FilteringWebHandler$DefaultGatewayFilterChain.lambda$filter$0(FilteringWebHandler.java:117)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:44)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at com.alibaba.csp.sentinel.adapter.reactor.MonoSentinelOperator.subscribe(MonoSentinelOperator.java:40)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:255)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:157)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:73)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:281)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:860)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:73)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1815)
	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:151)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:281)
	at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:860)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1815)
	at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:149)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2397)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.request(FluxOnAssembly.java:497)
	at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onSubscribe(MonoFilterWhen.java:112)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onSubscribe(FluxOnAssembly.java:481)
	at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:54)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
	at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:448)
	at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onNext(FluxConcatMap.java:250)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:98)
	at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:44)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onNext(FluxOnAssembly.java:387)
	at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:270)
	at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:228)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.request(FluxOnAssembly.java:497)
	at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.request(FluxDematerialize.java:127)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.request(FluxOnAssembly.java:497)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.request(FluxOnAssembly.java:497)
	at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:235)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onSubscribe(FluxOnAssembly.java:481)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onSubscribe(FluxOnAssembly.java:481)
	at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onSubscribe(FluxDematerialize.java:77)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onSubscribe(FluxOnAssembly.java:481)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:164)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
	at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
	at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
	at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:448)
	at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:218)
	at reactor.core.publisher.FluxOnAssembly$OnAssemblySubscriber.onSubscribe(FluxOnAssembly.java:481)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:164)
	at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
	at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
	at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:255)
	at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
	at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
	at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:915)
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:654)
	at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:478)
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:526)
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:94)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:209)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:311)
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:432)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-06-04 14:21:24.800|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:22:19.160|WARN| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 14:22:19.182|WARN| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilterAI版本:361|用户中文名缓存失败: 500001_admin
2025-06-04 14:22:19.214|ERROR| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilterAI版本:178|权限校验过程中发生异常
reactor.blockhound.BlockingOperationError: Blocking call! sun.misc.Unsafe#park
	at sun.misc.Unsafe.park(Unsafe.java)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoDefer] :
	reactor.core.publisher.Mono.defer(Mono.java:213)
	org.springframework.cloud.gateway.handler.FilteringWebHandler$DefaultGatewayFilterChain.filter(FilteringWebHandler.java:113)
Error has been observed at the following site(s):
	|_   Mono.defer ⇢ at org.springframework.cloud.gateway.handler.FilteringWebHandler$DefaultGatewayFilterChain.filter(FilteringWebHandler.java:113)
	|_ Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilterAI版本.lambda$null$3(AccessGatewayFilterAI版本.java:155)
	|_ Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilterAI版本.lambda$filter$4(AccessGatewayFilterAI版本.java:147)
	|_ Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilterAI版本.filter(AccessGatewayFilterAI版本.java:123)
Stack trace:
		at sun.misc.Unsafe.park(Unsafe.java)
		at java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
		at java.util.concurrent.CompletableFuture$Signaller.block(CompletableFuture.java:1693)
		at java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3323)
		at java.util.concurrent.CompletableFuture.waitingGet(CompletableFuture.java:1729)
		at java.util.concurrent.CompletableFuture.join(CompletableFuture.java:1934)
		at org.springframework.data.redis.connection.lettuce.LettuceFutureUtils.join(LettuceFutureUtils.java:68)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionProvider.getConnection(LettuceConnectionProvider.java:53)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1459)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1247)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1230)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getClusterConnection(LettuceConnectionFactory.java:378)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getConnection(LettuceConnectionFactory.java:355)
		at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193)
		at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144)
		at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105)
		at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:209)
		at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:189)
		at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
		at org.springframework.data.redis.core.DefaultSetOperations.isMember(DefaultSetOperations.java:203)
		at cn.hydee.gateway.filter.AccessGatewayFilterOld.filter(AccessGatewayFilterOld.java:143)
		at org.springframework.cloud.gateway.handler.FilteringWebHandler$GatewayFilterAdapter.filter(FilteringWebHandler.java:137)
		at org.springframework.cloud.gateway.filter.OrderedGatewayFilter.filter(OrderedGatewayFilter.java:44)
		at org.springframework.cloud.gateway.handler.FilteringWebHandler$DefaultGatewayFilterChain.lambda$filter$0(FilteringWebHandler.java:117)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:44)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:157)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1815)
		at reactor.core.publisher.MonoCollectList$MonoCollectListSubscriber.onComplete(MonoCollectList.java:128)
		at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.deferredComplete(FluxUsingWhen.java:405)
		at reactor.core.publisher.FluxUsingWhen$CommitInner.onComplete(FluxUsingWhen.java:540)
		at reactor.core.publisher.MonoIgnoreElements$IgnoreElementsSubscriber.onComplete(MonoIgnoreElements.java:88)
		at reactor.core.publisher.FluxFlatMap$FlatMapMain.checkTerminated(FluxFlatMap.java:824)
		at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:608)
		at reactor.core.publisher.FluxFlatMap$FlatMapMain.drain(FluxFlatMap.java:588)
		at reactor.core.publisher.FluxFlatMap$FlatMapMain.onComplete(FluxFlatMap.java:465)
		at reactor.core.publisher.FluxArray$ArraySubscription.slowPath(FluxArray.java:138)
		at reactor.core.publisher.FluxArray$ArraySubscription.request(FluxArray.java:100)
		at reactor.core.publisher.FluxFlatMap$FlatMapMain.onSubscribe(FluxFlatMap.java:371)
		at reactor.core.publisher.FluxMerge.subscribe(FluxMerge.java:69)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.onComplete(FluxUsingWhen.java:397)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onComplete(FluxMapFuseable.java:150)
		at reactor.core.publisher.FluxFlatMap$FlatMapMain.checkTerminated(FluxFlatMap.java:846)
		at reactor.core.publisher.FluxFlatMap$FlatMapMain.drainLoop(FluxFlatMap.java:608)
		at reactor.core.publisher.FluxFlatMap$FlatMapMain.innerComplete(FluxFlatMap.java:894)
		at reactor.core.publisher.FluxFlatMap$FlatMapInner.onComplete(FluxFlatMap.java:997)
		at io.lettuce.core.RedisPublisher$ImmediateSubscriber.onComplete(RedisPublisher.java:896)
		at io.lettuce.core.RedisPublisher$State.onAllDataRead(RedisPublisher.java:698)
		at io.lettuce.core.RedisPublisher$State$3.read(RedisPublisher.java:608)
		at io.lettuce.core.RedisPublisher$State$3.onDataAvailable(RedisPublisher.java:565)
		at io.lettuce.core.RedisPublisher$RedisSubscription.onDataAvailable(RedisPublisher.java:326)
		at io.lettuce.core.RedisPublisher$RedisSubscription.onAllDataRead(RedisPublisher.java:341)
		at io.lettuce.core.RedisPublisher$SubscriptionCommand.doOnComplete(RedisPublisher.java:778)
		at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:65)
		at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:63)
		at io.lettuce.core.cluster.ClusterCommand.complete(ClusterCommand.java:65)
		at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:63)
		at io.lettuce.core.protocol.CommandHandler.complete(CommandHandler.java:742)
		at io.lettuce.core.protocol.CommandHandler.decode(CommandHandler.java:677)
		at io.lettuce.core.protocol.CommandHandler.channelRead(CommandHandler.java:594)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
2025-06-04 14:22:19.216|WARN| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilterOld:300|用户中文名缓存失败: 500001_admin
2025-06-04 14:22:54.740|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 14:22:54.758|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 14800 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 14:22:54.758|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 14:22:56.266|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 14:22:56.271|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 14:22:56.311|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-04 14:22:56.483|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 14:22:56.486|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 14:22:56.863|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=32bd2808-22cc-3a45-b749-6867309bdb63
2025-06-04 14:22:56.942|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 14:22:56.955|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 14:23:05.211|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:23:09.255|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:23:13.291|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:23:17.322|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:23:17.323|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 14:23:21.350|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:23:25.396|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:23:25.396|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 14:23:25.458|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 14:23:25.965|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:23:25.973|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:23:26.006|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:23:26.140|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$b9d5bc6b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:23:26.159|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:23:26.161|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:23:26.163|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 14:23:29.426|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:23:33.450|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:23:37.452|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:23:41.453|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:23:45.455|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:23:45.487|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 14:23:46.977|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-04 14:23:46.978|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-04 14:23:46.978|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-04 14:23:46.978|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-04 14:23:46.978|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-04 14:23:46.978|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-04 14:23:46.978|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-04 14:23:46.978|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-04 14:23:46.979|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-04 14:23:46.979|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-04 14:23:46.979|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-04 14:23:46.979|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-04 14:23:46.979|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-04 14:23:47.177|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 14:23:47.240|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 14:23:47.313|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 14:23:47.371|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 14:23:47.411|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 14:23:47.437|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 14:23:47.645|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 14:23:47.645|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 14:23:47.656|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 14:23:47.656|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 14:23:48.155|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-04 14:23:48.159|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-04 14:23:48.423|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-04 14:23:48.575|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-04 14:23:48.694|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-04 14:23:49.178|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-04 14:23:49.354|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-04 14:23:49.459|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:23:49.828|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-04 14:23:50.107|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 57.041 seconds (JVM running for 59.981)
2025-06-04 14:23:50.108|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-04 14:23:50.108|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-04 14:23:50.112|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-04 14:23:50.177|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-04 14:24:03.668|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 14:24:12.984|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:24:23.018|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 14:24:48.987|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:25:01.891|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 14:25:56.988|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:27:16.848|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 14:28:00.992|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:28:08.700|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 14:28:09.222|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:28:15.765|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 14:28:37.458|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:29:15.767|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 14:30:21.992|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:30:40.514|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 14:33:21.625|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:33:25.628|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:33:32.318|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 14:33:54.042|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:35:25.670|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:36:06.738|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 14:36:54.314|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 14:48:44.508|ERROR| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:225|权限校验异常: userId=1695970231258714114, matchPath=234
java.lang.ClassCastException: Cannot cast java.lang.String to java.util.Date
	at java.lang.Class.cast(Class.java:3369)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoMapFuseable] :
	reactor.core.publisher.Mono.cast(Mono.java:1789)
	cn.hydee.gateway.filter.AccessGatewayFilter.checkUserPermissionReactive(AccessGatewayFilter.java:213)
Error has been observed at the following site(s):
	|_           Mono.cast ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.checkUserPermissionReactive(AccessGatewayFilter.java:213)
	|_            Mono.map ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.checkUserPermissionReactive(AccessGatewayFilter.java:214)
	|_ Mono.defaultIfEmpty ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.checkUserPermissionReactive(AccessGatewayFilter.java:223)
Stack trace:
		at java.lang.Class.cast(Class.java:3369)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:113)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.onNext(FluxUsingWhen.java:358)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.drainAsync(FluxFlattenIterable.java:421)
		at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.drain(FluxFlattenIterable.java:686)
		at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.onNext(FluxFlattenIterable.java:250)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyInner.onNext(MonoFlatMapMany.java:250)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:281)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:860)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at io.lettuce.core.RedisPublisher$ImmediateSubscriber.onNext(RedisPublisher.java:886)
		at io.lettuce.core.RedisPublisher$RedisSubscription.onNext(RedisPublisher.java:291)
		at io.lettuce.core.RedisPublisher$SubscriptionCommand.doOnComplete(RedisPublisher.java:773)
		at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:65)
		at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:63)
		at io.lettuce.core.cluster.ClusterCommand.complete(ClusterCommand.java:65)
		at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:63)
		at io.lettuce.core.protocol.CommandHandler.complete(CommandHandler.java:742)
		at io.lettuce.core.protocol.CommandHandler.decode(CommandHandler.java:677)
		at io.lettuce.core.protocol.CommandHandler.channelRead(CommandHandler.java:594)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
2025-06-04 14:48:44.509|ERROR| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:179|权限校验过程中发生异常
java.lang.ClassCastException: Cannot cast java.lang.String to java.util.Date
	at java.lang.Class.cast(Class.java:3369)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.MonoMapFuseable] :
	reactor.core.publisher.Mono.cast(Mono.java:1789)
	cn.hydee.gateway.filter.AccessGatewayFilter.checkUserPermissionReactive(AccessGatewayFilter.java:213)
Error has been observed at the following site(s):
	|_           Mono.cast ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.checkUserPermissionReactive(AccessGatewayFilter.java:213)
	|_            Mono.map ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.checkUserPermissionReactive(AccessGatewayFilter.java:214)
	|_ Mono.defaultIfEmpty ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.checkUserPermissionReactive(AccessGatewayFilter.java:223)
	|_      Mono.doOnError ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.checkUserPermissionReactive(AccessGatewayFilter.java:224)
	|_        Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.lambda$null$2(AccessGatewayFilter.java:163)
	|_        Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.lambda$null$3(AccessGatewayFilter.java:156)
	|_        Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.lambda$filter$4(AccessGatewayFilter.java:148)
	|_        Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.filter(AccessGatewayFilter.java:124)
Stack trace:
		at java.lang.Class.cast(Class.java:3369)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:113)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxUsingWhen$UsingWhenSubscriber.onNext(FluxUsingWhen.java:358)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.drainAsync(FluxFlattenIterable.java:421)
		at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.drain(FluxFlattenIterable.java:686)
		at reactor.core.publisher.FluxFlattenIterable$FlattenIterableSubscriber.onNext(FluxFlattenIterable.java:250)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoFlatMapMany$FlatMapManyInner.onNext(MonoFlatMapMany.java:250)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:281)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:860)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at io.lettuce.core.RedisPublisher$ImmediateSubscriber.onNext(RedisPublisher.java:886)
		at io.lettuce.core.RedisPublisher$RedisSubscription.onNext(RedisPublisher.java:291)
		at io.lettuce.core.RedisPublisher$SubscriptionCommand.doOnComplete(RedisPublisher.java:773)
		at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:65)
		at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:63)
		at io.lettuce.core.cluster.ClusterCommand.complete(ClusterCommand.java:65)
		at io.lettuce.core.protocol.CommandWrapper.complete(CommandWrapper.java:63)
		at io.lettuce.core.protocol.CommandHandler.complete(CommandHandler.java:742)
		at io.lettuce.core.protocol.CommandHandler.decode(CommandHandler.java:677)
		at io.lettuce.core.protocol.CommandHandler.channelRead(CommandHandler.java:594)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
2025-06-04 14:48:45.468|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:48:49.470|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 14:48:58.745|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 15:08:25.367|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:363|用户中文名缓存失败: 500001_admin
2025-06-04 15:08:39.985|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:09:00.902|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:09:08.906|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:09:16.921|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:09:24.994|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:09:29.016|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:09:33.053|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:09:37.093|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:09:41.096|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:09:45.105|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:09:49.108|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:09:53.109|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:09:57.112|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:10:01.114|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:11:04.906|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:13:06.242|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:13:06.258|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 29852 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:13:06.259|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:13:07.611|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:13:07.615|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:13:07.655|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-06-04 15:13:07.827|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:13:07.830|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:13:08.145|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=32bd2808-22cc-3a45-b749-6867309bdb63
2025-06-04 15:13:08.221|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:13:08.225|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:13:16.447|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:13:20.501|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:13:24.536|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:13:28.594|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:13:28.594|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:13:32.644|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:13:36.683|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:13:36.683|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:13:36.748|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:13:37.230|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:13:37.235|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:13:37.264|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:13:37.414|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$7296b707] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:13:37.433|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:13:37.434|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:13:37.436|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:13:40.721|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:13:44.746|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:13:48.748|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:13:52.750|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:13:56.752|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:13:56.784|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 15:13:58.283|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-04 15:13:58.283|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-04 15:13:58.284|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-04 15:13:58.284|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-04 15:13:58.284|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-04 15:13:58.284|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-04 15:13:58.285|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-04 15:13:58.285|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-04 15:13:58.285|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-04 15:13:58.285|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-04 15:13:58.285|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-04 15:13:58.285|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-04 15:13:58.286|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-04 15:13:58.484|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:13:58.548|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:13:58.621|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:13:58.682|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:13:58.725|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:13:58.748|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:13:58.816|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:13:58.817|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:13:58.824|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:13:58.825|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:13:59.398|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-04 15:13:59.401|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-04 15:13:59.559|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-04 15:13:59.704|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-04 15:13:59.824|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-04 15:14:00.338|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-04 15:14:00.523|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-04 15:14:00.755|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:14:01.012|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-04 15:14:01.361|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.257 seconds (JVM running for 59.248)
2025-06-04 15:14:01.362|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-04 15:14:01.362|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-04 15:14:01.367|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-04 15:14:01.467|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-04 15:14:03.389|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:367|用户中文名缓存失败: 500001_admin
2025-06-04 15:14:20.890|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:14:47.019|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:367|用户中文名缓存失败: 500001_admin
2025-06-04 15:15:32.074|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:15:32.097|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 11912 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:15:32.097|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:15:33.436|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:15:33.441|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:15:33.481|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-06-04 15:15:33.654|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:15:33.657|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:15:34.356|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=32bd2808-22cc-3a45-b749-6867309bdb63
2025-06-04 15:15:34.441|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:15:34.455|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:15:42.675|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:15:46.714|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:15:50.737|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:15:54.765|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:15:54.765|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:15:58.782|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:16:02.825|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:16:02.826|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:16:02.875|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:16:03.353|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:16:03.358|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:16:03.386|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:16:03.504|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$939f8e49] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:16:03.522|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:16:03.524|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:16:03.526|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:16:06.857|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:16:10.892|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:16:14.933|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:16:18.963|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:16:23.001|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:16:23.031|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 15:16:24.485|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-04 15:16:24.486|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-04 15:16:24.486|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-04 15:16:24.486|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-04 15:16:24.487|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-04 15:16:24.487|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-04 15:16:24.487|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-04 15:16:24.487|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-04 15:16:24.487|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-04 15:16:24.488|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-04 15:16:24.488|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-04 15:16:24.488|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-04 15:16:24.488|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-04 15:16:24.718|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:16:24.781|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:16:24.854|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:16:24.913|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:16:24.956|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:16:24.980|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:16:25.174|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:16:25.175|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:16:25.186|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:16:25.186|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:16:25.654|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-04 15:16:25.656|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-04 15:16:25.854|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-04 15:16:26.019|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-04 15:16:26.133|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-04 15:16:26.731|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-04 15:16:26.894|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-04 15:16:27.033|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:16:27.443|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-04 15:16:27.708|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.805 seconds (JVM running for 59.678)
2025-06-04 15:16:27.709|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-04 15:16:27.709|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-04 15:16:27.713|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-04 15:16:27.771|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-04 15:16:31.473|WARN| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilter:365|用户中文名缓存失败: 500001_admin
2025-06-04 15:17:34.154|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:18:10.166|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:19:18.168|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:20:46.692|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:21:14.896|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:21:22.173|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:22:22.893|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:22:22.921|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 29216 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:22:22.922|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:22:24.231|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:22:24.235|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:22:24.289|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 36 ms. Found 0 Redis repository interfaces.
2025-06-04 15:22:24.494|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:22:24.497|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:22:24.795|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=32bd2808-22cc-3a45-b749-6867309bdb63
2025-06-04 15:22:24.863|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:22:24.866|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:22:33.095|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:22:37.146|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:22:41.194|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:22:45.226|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:22:45.226|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:22:49.263|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:22:53.311|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:22:53.311|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:22:53.356|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:22:53.790|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:22:53.795|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:22:53.824|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:22:53.940|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$f2637e5f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:22:53.958|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:22:53.959|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:22:53.961|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:22:57.350|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:23:01.353|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:23:05.356|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:23:09.358|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:23:13.360|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:23:13.401|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 15:23:14.970|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-04 15:23:14.971|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-04 15:23:14.971|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-04 15:23:14.971|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-04 15:23:14.971|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-04 15:23:14.971|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-04 15:23:14.972|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-04 15:23:14.972|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-04 15:23:14.972|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-04 15:23:14.972|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-04 15:23:14.973|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-04 15:23:14.973|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-04 15:23:14.973|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-04 15:23:15.175|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:23:15.247|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:23:15.322|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:23:15.377|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:23:15.422|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:23:15.448|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:23:15.514|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:23:15.514|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:23:15.523|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:23:15.523|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:23:16.098|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-04 15:23:16.100|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-04 15:23:16.291|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-04 15:23:16.443|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-04 15:23:16.559|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-04 15:23:17.051|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-04 15:23:17.222|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-04 15:23:17.361|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:23:17.683|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-04 15:23:17.958|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.268 seconds (JVM running for 58.871)
2025-06-04 15:23:17.959|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-04 15:23:17.960|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-04 15:23:17.963|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-04 15:23:18.024|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-04 15:23:24.708|WARN| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilter:366|用户中文名缓存失败: 500001_admin
2025-06-04 15:23:50.513|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:24:03.730|WARN| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilter:366|用户中文名缓存失败: 500001_admin
2025-06-04 15:25:05.179|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:25:05.197|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 2464 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:25:05.197|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:25:06.492|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:25:06.496|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:25:06.549|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-04 15:25:06.759|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:25:06.762|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:25:07.149|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=32bd2808-22cc-3a45-b749-6867309bdb63
2025-06-04 15:25:07.221|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:25:07.224|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:25:15.460|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:25:19.515|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:25:23.559|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:25:27.580|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:25:27.581|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:25:31.611|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:25:35.643|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:25:35.643|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:25:35.692|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:25:36.142|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:25:36.146|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:25:36.173|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:25:36.310|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$a7aa1f2e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:25:36.329|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:25:36.330|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:25:36.332|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:25:39.675|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:25:43.696|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:25:47.698|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:25:51.701|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:25:55.703|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:25:55.731|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 15:25:57.304|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-04 15:25:57.304|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-04 15:25:57.304|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-04 15:25:57.304|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-04 15:25:57.305|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-04 15:25:57.305|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-04 15:25:57.305|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-04 15:25:57.305|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-04 15:25:57.305|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-04 15:25:57.305|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-04 15:25:57.305|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-04 15:25:57.306|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-04 15:25:57.306|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-04 15:25:57.548|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:25:57.619|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:25:57.691|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:25:57.754|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:25:57.797|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:25:57.822|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:25:58.026|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:25:58.027|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:25:58.036|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:25:58.036|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:25:58.467|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-04 15:25:58.469|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-04 15:25:58.663|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-04 15:25:58.815|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-04 15:25:58.930|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-04 15:25:59.408|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-04 15:25:59.585|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-04 15:25:59.705|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:26:00.066|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-04 15:26:00.339|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.4 seconds (JVM running for 58.956)
2025-06-04 15:26:00.340|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-04 15:26:00.340|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-04 15:26:00.344|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-04 15:26:00.405|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-04 15:26:08.286|WARN| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilter:367|用户中文名缓存失败: 500001_admin
2025-06-04 15:31:34.963|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:32:17.300|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:32:17.319|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 27888 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:32:17.319|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:32:18.619|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:32:18.624|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:32:18.660|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-04 15:32:18.838|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:32:18.841|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:32:19.158|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=32bd2808-22cc-3a45-b749-6867309bdb63
2025-06-04 15:32:19.231|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:32:19.236|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:32:27.481|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:32:31.527|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:32:35.578|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:32:39.616|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:32:39.616|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:32:43.654|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:32:47.686|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:32:47.687|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:32:47.737|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:32:48.152|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:32:48.157|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:32:48.184|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:32:48.309|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$78406745] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:32:48.328|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:32:48.329|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:32:48.332|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:32:51.731|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:32:55.758|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:32:59.760|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:33:03.762|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:33:07.764|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:33:07.794|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 15:33:08.220|WARN| N/A||main|o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accessGatewayFilter': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.data.redis.core.ReactiveRedisTemplate<java.lang.String, java.util.Date>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}
2025-06-04 15:33:08.406|INFO| N/A||main|o.s.b.a.l.ConditionEvaluationReportLoggingListener:136|

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-04 15:33:08.444|ERROR| N/A||main|o.s.b.d.LoggingFailureAnalysisReporter:40|

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'org.springframework.data.redis.core.ReactiveRedisTemplate' that could not be found.


Action:

Consider defining a bean of type 'org.springframework.data.redis.core.ReactiveRedisTemplate' in your configuration.

2025-06-04 15:33:11.767|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:33:31.769|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:34:07.772|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:35:15.774|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:35:26.132|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:35:26.154|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 6468 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:35:26.155|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:35:27.581|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:35:27.585|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:35:27.622|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-04 15:35:27.809|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:35:27.812|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:35:28.148|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=437085b1-d5ce-3b86-8eeb-6b19715f8155
2025-06-04 15:35:28.225|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:35:28.228|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:35:36.455|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:35:40.503|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:35:44.545|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:35:48.575|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:35:48.576|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:35:52.611|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:35:56.644|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:35:56.644|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:35:56.693|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:35:57.129|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:35:57.134|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:35:57.178|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:35:57.317|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$69d5c8a2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:35:57.338|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:35:57.349|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:35:57.354|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:36:00.678|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:36:04.679|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:36:08.681|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:36:12.682|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:36:16.684|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:36:16.715|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 15:36:17.142|WARN| N/A||main|o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accessGatewayFilter': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'reactiveRedisTemplateDate' defined in class path resource [cn/hydee/gateway/config/ReactiveRedisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.data.redis.core.ReactiveRedisTemplate]: Factory method 'reactiveRedisTemplateDate' threw exception; nested exception is java.lang.IllegalArgumentException: Key SerializationPair must not be null!
2025-06-04 15:36:17.304|INFO| N/A||main|o.s.b.a.l.ConditionEvaluationReportLoggingListener:136|

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-04 15:36:17.343|ERROR| N/A||main|o.s.boot.SpringApplication:860|Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accessGatewayFilter': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'reactiveRedisTemplateDate' defined in class path resource [cn/hydee/gateway/config/ReactiveRedisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.data.redis.core.ReactiveRedisTemplate]: Factory method 'reactiveRedisTemplateDate' threw exception; nested exception is java.lang.IllegalArgumentException: Key SerializationPair must not be null!
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.refresh(ReactiveWebServerApplicationContext.java:63)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at cn.hydee.gateway.GateWayApplication.main(GateWayApplication.java:26)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'reactiveRedisTemplateDate' defined in class path resource [cn/hydee/gateway/config/ReactiveRedisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.data.redis.core.ReactiveRedisTemplate]: Factory method 'reactiveRedisTemplateDate' threw exception; nested exception is java.lang.IllegalArgumentException: Key SerializationPair must not be null!
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:461)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	... 18 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.data.redis.core.ReactiveRedisTemplate]: Factory method 'reactiveRedisTemplateDate' threw exception; nested exception is java.lang.IllegalArgumentException: Key SerializationPair must not be null!
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 34 common frames omitted
Caused by: java.lang.IllegalArgumentException: Key SerializationPair must not be null!
	at org.springframework.util.Assert.notNull(Assert.java:201)
	at org.springframework.data.redis.serializer.DefaultRedisSerializationContext$DefaultRedisSerializationContextBuilder.build(DefaultRedisSerializationContext.java:169)
	at cn.hydee.gateway.config.ReactiveRedisConfig.reactiveRedisTemplateDate(ReactiveRedisConfig.java:42)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 35 common frames omitted
2025-06-04 15:36:20.686|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:36:40.689|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:37:16.692|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:38:01.300|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:38:01.319|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 27768 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:38:01.320|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:38:02.666|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:38:02.671|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:38:02.708|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-04 15:38:02.922|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:38:02.924|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:38:03.234|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=32bd2808-22cc-3a45-b749-6867309bdb63
2025-06-04 15:38:03.306|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:38:03.319|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:38:11.544|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:38:15.577|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:38:19.596|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:38:23.625|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:38:23.625|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:38:27.666|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:38:31.713|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:38:31.714|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:38:31.762|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:38:32.183|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:38:32.189|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:38:32.219|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:38:32.362|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$69789267] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:38:32.382|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:38:32.383|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:38:32.385|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:38:35.744|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:38:39.747|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:38:43.750|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:38:47.753|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:38:51.755|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:38:51.789|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 15:38:52.291|WARN| N/A||main|o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accessGatewayFilter': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'org.springframework.data.redis.core.ReactiveRedisTemplate<java.lang.String, java.util.Date>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}
2025-06-04 15:38:52.503|INFO| N/A||main|o.s.b.a.l.ConditionEvaluationReportLoggingListener:136|

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-04 15:38:52.548|ERROR| N/A||main|o.s.b.d.LoggingFailureAnalysisReporter:40|

***************************
APPLICATION FAILED TO START
***************************

Description:

A component required a bean of type 'org.springframework.data.redis.core.ReactiveRedisTemplate' that could not be found.


Action:

Consider defining a bean of type 'org.springframework.data.redis.core.ReactiveRedisTemplate' in your configuration.

2025-06-04 15:38:55.759|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:39:15.762|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:39:49.356|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:39:49.372|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 31176 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:39:49.373|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:39:50.735|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:39:50.738|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:39:50.789|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-06-04 15:39:50.981|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:39:50.983|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:39:51.400|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=437085b1-d5ce-3b86-8eeb-6b19715f8155
2025-06-04 15:39:51.505|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:39:51.509|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:39:59.742|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:40:03.779|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:40:07.820|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:40:11.868|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:40:11.868|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:40:15.899|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:40:19.933|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:40:19.933|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:40:19.981|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:40:20.378|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:40:20.383|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:40:20.411|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:40:20.526|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$506e053b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:40:20.543|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:40:20.544|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:40:20.546|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:40:23.973|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:40:27.987|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:40:37.199|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:41:00.464|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:41:00.508|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 15204 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:41:00.509|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:41:01.833|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:41:01.839|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:41:01.889|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-06-04 15:41:02.116|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:41:02.119|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:41:02.393|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=1e4350e4-9c0c-362a-9e88-9f0544bbcb4a
2025-06-04 15:41:02.477|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:41:02.480|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:41:10.705|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:41:14.752|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:41:18.784|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:41:22.825|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:41:22.826|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:41:26.853|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:41:30.889|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:41:30.889|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:41:30.947|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:41:31.421|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:41:31.428|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:41:31.463|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:41:31.598|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$18d025eb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:41:31.619|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:41:31.620|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:41:31.622|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:41:34.924|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:41:38.963|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:41:43.002|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:41:47.029|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:41:51.068|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:41:51.100|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 15:41:51.522|WARN| N/A||main|o.s.b.w.r.c.AnnotationConfigReactiveWebServerApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accessGatewayFilter': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'reactiveRedisTemplateObject' defined in class path resource [cn/hydee/gateway/config/ReactiveRedisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.data.redis.core.ReactiveRedisTemplate]: Factory method 'reactiveRedisTemplateObject' threw exception; nested exception is java.lang.IllegalArgumentException: Key SerializationPair must not be null!
2025-06-04 15:41:51.888|INFO| N/A||main|o.s.b.a.l.ConditionEvaluationReportLoggingListener:136|

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-04 15:41:51.922|ERROR| N/A||main|o.s.boot.SpringApplication:860|Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'accessGatewayFilter': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'reactiveRedisTemplateObject' defined in class path resource [cn/hydee/gateway/config/ReactiveRedisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.data.redis.core.ReactiveRedisTemplate]: Factory method 'reactiveRedisTemplateObject' threw exception; nested exception is java.lang.IllegalArgumentException: Key SerializationPair must not be null!
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:321)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1413)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:601)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.refresh(ReactiveWebServerApplicationContext.java:63)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:771)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:763)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:438)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:339)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1329)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1318)
	at cn.hydee.gateway.GateWayApplication.main(GateWayApplication.java:26)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'reactiveRedisTemplateObject' defined in class path resource [cn/hydee/gateway/config/ReactiveRedisConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.data.redis.core.ReactiveRedisTemplate]: Factory method 'reactiveRedisTemplateObject' threw exception; nested exception is java.lang.IllegalArgumentException: Key SerializationPair must not be null!
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.resolveBeanByName(AbstractAutowireCapableBeanFactory.java:461)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:527)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:497)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:650)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:318)
	... 18 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.data.redis.core.ReactiveRedisTemplate]: Factory method 'reactiveRedisTemplateObject' threw exception; nested exception is java.lang.IllegalArgumentException: Key SerializationPair must not be null!
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 34 common frames omitted
Caused by: java.lang.IllegalArgumentException: Key SerializationPair must not be null!
	at org.springframework.util.Assert.notNull(Assert.java:201)
	at org.springframework.data.redis.serializer.DefaultRedisSerializationContext$DefaultRedisSerializationContextBuilder.build(DefaultRedisSerializationContext.java:169)
	at cn.hydee.gateway.config.ReactiveRedisConfig.reactiveRedisTemplateObject(ReactiveRedisConfig.java:47)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 35 common frames omitted
2025-06-04 15:41:55.116|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:42:15.161|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:42:51.209|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:43:59.253|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:45:14.610|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:45:14.647|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 23260 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:45:14.655|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:45:16.018|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:45:16.022|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:45:16.057|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-06-04 15:45:16.235|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:45:16.237|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:45:16.565|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=1e4350e4-9c0c-362a-9e88-9f0544bbcb4a
2025-06-04 15:45:16.663|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:45:16.666|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:45:24.869|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:45:28.907|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:45:32.941|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:45:36.978|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:45:36.978|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:45:41.029|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:45:45.069|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:45:45.069|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:45:45.118|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:45:45.532|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:45:45.537|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:45:45.565|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:45:45.737|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$3c84e958] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:45:45.764|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:45:45.766|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:45:45.769|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:45:49.105|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:45:53.108|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:45:57.110|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:46:01.113|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:46:05.117|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:46:05.161|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 15:46:06.922|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-04 15:46:06.922|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-04 15:46:06.923|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-04 15:46:06.923|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-04 15:46:06.923|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-04 15:46:06.923|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-04 15:46:06.923|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-04 15:46:06.923|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-04 15:46:06.923|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-04 15:46:06.924|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-04 15:46:06.924|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-04 15:46:06.924|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-04 15:46:06.924|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-04 15:46:07.136|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:46:07.212|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:46:07.287|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:46:07.345|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:46:07.389|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:46:07.549|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:46:07.624|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:46:07.624|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:46:07.635|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:46:07.635|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:46:08.088|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-04 15:46:08.090|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-04 15:46:08.298|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-04 15:46:08.457|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-04 15:46:08.587|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-04 15:46:09.097|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-04 15:46:09.119|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:46:09.276|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-04 15:46:09.775|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-04 15:46:10.053|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.621 seconds (JVM running for 59.597)
2025-06-04 15:46:10.055|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-04 15:46:10.055|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-04 15:46:10.059|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-04 15:46:10.126|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-04 15:46:13.689|WARN| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilter:372|用户中文名缓存失败: 500001_admin
2025-06-04 15:46:29.125|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:00.773|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:47:00.806|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 960 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:47:00.806|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:47:02.130|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:47:02.135|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:47:02.171|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-04 15:47:02.353|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:47:02.356|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:47:02.697|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=1e4350e4-9c0c-362a-9e88-9f0544bbcb4a
2025-06-04 15:47:02.805|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:47:02.823|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:47:11.044|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:15.108|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:19.152|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:23.190|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:23.190|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:47:27.228|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:31.268|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:31.269|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:47:31.319|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:47:31.748|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:47:31.753|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:47:31.783|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:47:31.930|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$fa32e697] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:47:31.952|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:47:31.953|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:47:31.955|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:47:35.291|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:39.293|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:43.295|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:47.298|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:51.302|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:51.333|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 15:47:52.928|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-04 15:47:52.929|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-04 15:47:52.929|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-04 15:47:52.929|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-04 15:47:52.929|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-04 15:47:52.930|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-04 15:47:52.930|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-04 15:47:52.930|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-04 15:47:52.930|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-04 15:47:52.930|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-04 15:47:52.930|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-04 15:47:52.931|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-04 15:47:52.931|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-04 15:47:53.137|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:47:53.210|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:47:53.285|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:47:53.346|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:47:53.391|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:47:53.541|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:47:53.614|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:47:53.614|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:47:53.623|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:47:53.624|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:47:54.080|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-04 15:47:54.083|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-04 15:47:54.252|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-04 15:47:54.411|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-04 15:47:54.528|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-04 15:47:55.018|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-04 15:47:55.181|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-04 15:47:55.304|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:47:55.665|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-04 15:47:56.069|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.459 seconds (JVM running for 59.187)
2025-06-04 15:47:56.071|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-04 15:47:56.071|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-04 15:47:56.077|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-04 15:47:56.212|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-04 15:48:01.598|WARN| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilter:375|用户中文名缓存失败: 500001_admin
2025-06-04 15:48:15.310|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:48:51.318|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:49:05.613|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:49:05.650|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 31276 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:49:05.650|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:49:06.951|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:49:06.955|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:49:07.005|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-06-04 15:49:07.197|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:49:07.199|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:49:07.462|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=32bd2808-22cc-3a45-b749-6867309bdb63
2025-06-04 15:49:07.534|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:49:07.537|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:49:15.780|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:49:19.816|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:49:23.858|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:49:27.892|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:49:27.892|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:49:31.930|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:49:35.967|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:49:35.967|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:49:36.020|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:49:36.455|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:49:36.460|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:49:36.492|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:49:36.632|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$364a32ff] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:49:36.651|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:49:36.652|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:49:36.654|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:49:40.018|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:49:44.021|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:49:48.023|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:49:52.026|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:49:56.030|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:49:56.060|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 15:49:57.600|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-04 15:49:57.600|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-04 15:49:57.600|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-04 15:49:57.600|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-04 15:49:57.601|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-04 15:49:57.601|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-04 15:49:57.601|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-04 15:49:57.601|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-04 15:49:57.601|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-04 15:49:57.602|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-04 15:49:57.602|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-04 15:49:57.602|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-04 15:49:57.602|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-04 15:49:57.819|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:49:57.897|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:49:57.976|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:49:58.032|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:49:58.090|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:49:58.119|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:49:58.299|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:49:58.300|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:49:58.309|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:49:58.309|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:49:58.753|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-04 15:49:58.756|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-04 15:49:58.934|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-04 15:49:59.087|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-04 15:49:59.198|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-04 15:49:59.849|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-04 15:50:00.033|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:50:00.051|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-04 15:50:00.548|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-04 15:50:00.829|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.392 seconds (JVM running for 59.214)
2025-06-04 15:50:00.830|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-04 15:50:00.830|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-04 15:50:00.834|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-04 15:50:00.891|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-04 15:50:20.073|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:50:40.082|WARN| N/A||lettuce-nioEventLoop-5-9|c.h.g.filter.AccessGatewayFilter:368|用户中文名缓存失败: 500001_admin
2025-06-04 15:55:43.315|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 15:55:43.329|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 24280 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 15:55:43.329|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 15:55:44.636|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 15:55:44.640|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:55:44.679|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-06-04 15:55:44.878|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 15:55:44.880|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 15:55:45.265|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=32bd2808-22cc-3a45-b749-6867309bdb63
2025-06-04 15:55:45.347|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 15:55:45.360|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 15:55:53.613|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:55:57.665|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:56:01.707|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:56:05.738|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:56:05.739|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 15:56:09.785|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:56:13.826|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:56:13.827|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 15:56:13.885|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 15:56:14.348|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:56:14.353|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:56:14.386|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:56:14.529|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$d23df3b8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:56:14.549|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:56:14.550|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:56:14.553|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 15:56:17.862|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:56:21.898|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:56:25.934|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:56:29.971|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:56:34.021|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:56:34.048|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 15:56:35.560|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-04 15:56:35.561|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-04 15:56:35.561|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-04 15:56:35.561|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-04 15:56:35.561|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-04 15:56:35.561|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-04 15:56:35.561|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-04 15:56:35.562|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-04 15:56:35.562|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-04 15:56:35.562|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-04 15:56:35.562|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-04 15:56:35.562|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-04 15:56:35.563|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-04 15:56:35.762|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:56:35.822|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:56:35.895|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:56:35.954|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:56:36.001|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:56:36.029|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 15:56:36.247|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:56:36.247|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:56:36.259|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 15:56:36.259|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 15:56:36.725|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-04 15:56:36.728|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-04 15:56:36.900|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-04 15:56:37.045|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-04 15:56:37.152|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-04 15:56:37.663|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-04 15:56:37.841|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-04 15:56:38.183|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:56:38.309|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-04 15:56:38.595|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.432 seconds (JVM running for 59.196)
2025-06-04 15:56:38.596|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-04 15:56:38.596|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-04 15:56:38.599|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-04 15:56:38.674|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-04 15:56:49.751|WARN| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilter:373|用户中文名缓存失败: 500001_admin
2025-06-04 15:57:35.140|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:58:11.149|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 15:59:19.160|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:00:57.656|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:01:23.201|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:01:27.242|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:03:27.229|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:05:31.244|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:05:57.686|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:06:25.963|WARN| N/A||Apollo-RemoteConfigRepository-1|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:07:35.258|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:09:30.378|WARN| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilter:373|用户中文名缓存失败: 500001_admin
2025-06-04 16:09:43.750|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:10:05.732|WARN| N/A||lettuce-nioEventLoop-5-8|c.h.g.filter.AccessGatewayFilter:373|用户中文名缓存失败: 500001_admin
2025-06-04 16:10:05.945|WARN| N/A||scheduling-1|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 16:10:05.960|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:05.970|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:05.981|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:05.989|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:05.999|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.008|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.017|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.026|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.054|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.078|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.101|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.130|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.158|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.174|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.188|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.216|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.240|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.256|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.271|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.284|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.295|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.306|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.316|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.328|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.339|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.351|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.361|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.374|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.386|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.397|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.405|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.415|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.423|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.434|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.442|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.451|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.462|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.470|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.479|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.490|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.498|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.508|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.518|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.525|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.534|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.546|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.555|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.566|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.576|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.585|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.595|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.605|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.615|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.625|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.636|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.645|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.656|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.666|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.674|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.682|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.691|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.700|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.708|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.719|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.729|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.736|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.744|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.765|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.775|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.784|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.792|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.801|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.812|WARN| N/A||scheduling-1|o.s.c.a.AnnotationConfigApplicationContext:591|Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
2025-06-04 16:10:06.837|ERROR| N/A||scheduling-1|DataLoader:67|#$$查询应用配置数据失败：
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'discoveryClientServiceInstanceListSupplier' defined in org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1177)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:564)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:524)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:944)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.cloud.context.named.NamedContextFactory.createContext(NamedContextFactory.java:133)
	at org.springframework.cloud.context.named.NamedContextFactory.getContext(NamedContextFactory.java:101)
	at org.springframework.cloud.context.named.NamedContextFactory.getInstances(NamedContextFactory.java:181)
	at org.springframework.cloud.openfeign.loadbalancer.FeignBlockingLoadBalancerClient.execute(FeignBlockingLoadBalancerClient.java:85)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy189.enableAppConfigPage(Unknown Source)
	at com.yxt.safecenter.auth.sdk.service.SafeCenterDataService.lambda$refreshData$1(SafeCenterDataService.java:103)
	at com.yxt.safecenter.auth.sdk.utils.PageUtils.pageQueryALL(PageUtils.java:28)
	at com.yxt.safecenter.auth.sdk.service.SafeCenterDataService.refreshData(SafeCenterDataService.java:96)
	at com.yxt.safecenter.auth.sdk.job.SafeCenterDataRefreshJob.executeTask(SafeCenterDataRefreshJob.java:25)
	at sun.reflect.GeneratedMethodAccessor190.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:95)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier]: Factory method 'discoveryClientServiceInstanceListSupplier' threw exception; nested exception is org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 38 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'reactiveCompositeDiscoveryClient': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1273)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1257)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:500)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1172)
	at org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplierBuilder.lambda$withDiscoveryClient$1(ServiceInstanceListSupplierBuilder.java:91)
	at org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplierBuilder.build(ServiceInstanceListSupplierBuilder.java:260)
	at org.springframework.cloud.loadbalancer.annotation.GreyLoadBalancerClientConfiguration$ReactiveSupportConfiguration.discoveryClientServiceInstanceListSupplier(GreyLoadBalancerClientConfiguration.java:52)
	at sun.reflect.GeneratedMethodAccessor195.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 39 common frames omitted
2025-06-04 16:10:17.191|INFO| N/A||background-preinit|o.h.validator.internal.util.Version:21|HV000001: Hibernate Validator 6.1.7.Final
2025-06-04 16:10:17.227|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:55|Starting GateWayApplication using Java 1.8.0_202 on hdyf-yangrunkang with PID 24896 (D:\github\codes\businesses-gateway\target\classes started by Lenovo in D:\github\codes\businesses-gateway)
2025-06-04 16:10:17.228|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:668|The following profiles are active: local
2025-06-04 16:10:18.568|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:250|Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-04 16:10:18.572|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:128|Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 16:10:18.621|INFO| N/A||main|o.s.d.r.c.RepositoryConfigurationDelegate:188|Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-04 16:10:18.808|INFO| N/A||main|c.c.f.f.i.p.DefaultApplicationProvider:116|App ID is set to businesses-gateway by app.id property from /META-INF/app.properties
2025-06-04 16:10:18.810|INFO| N/A||main|c.c.f.f.i.p.DefaultServerProvider:176|Environment is set to null. Because it is not available in either (1) JVM system property 'env', (2) OS env variable 'ENV' nor (3) property 'env' from the properties InputStream.
2025-06-04 16:10:19.089|INFO| N/A||main|o.s.cloud.context.scope.GenericScope:283|BeanFactory id=32bd2808-22cc-3a45-b749-6867309bdb63
2025-06-04 16:10:19.158|INFO| N/A||main|c.c.f.a.i.DefaultMetaServerProvider:42|Located meta services from apollo.meta configuration: http://*************:9100!
2025-06-04 16:10:19.161|INFO| N/A||main|c.c.f.apollo.core.MetaDomainConsts:93|Located meta server address http://*************:9100 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider
2025-06-04 16:10:27.398|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:10:31.437|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:10:35.486|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 1 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:10:39.515|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:10:39.516|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.LocalFileConfigRepository, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties]
2025-06-04 16:10:43.546|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 2 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:10:47.591|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:10:47.592|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:61|Init Apollo Local Config failed - namespace: application, reason: Load config from local config failed! [Cause: Cannot read from local cache file C:\opt\data\businesses-gateway\config-cache\businesses-gateway+default+application.properties].
2025-06-04 16:10:47.639|WARN| N/A||main|c.c.f.apollo.internals.DefaultConfig:95|Could not load config for namespace application from Apollo, please check whether the configs are released in Apollo! Return default value now!
2025-06-04 16:10:48.077|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence' of type [org.hibernate.validator.internal.constraintvalidators.bv.notempty.NotEmptyValidatorForCharSequence] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 16:10:48.081|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator' of type [org.hibernate.validator.internal.constraintvalidators.bv.NotNullValidator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 16:10:48.109|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 16:10:48.229|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration$$EnhancerBySpringCGLIB$$d1e90887] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 16:10:48.247|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 16:10:48.249|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 16:10:48.251|INFO| N/A||main|o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376|Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-04 16:10:51.645|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 4 seconds. appId: businesses-gateway, cluster: default, namespaces: application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:10:55.662|WARN| N/A||main|c.c.f.a.i.AbstractConfigRepository:29|Sync config failed, will retry. Repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:10:59.665|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 8 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:11:03.668|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:11:07.671|WARN| N/A||main|c.c.f.a.i.LocalFileConfigRepository:167|Sync config from upstream repository class com.ctrip.framework.apollo.internals.RemoteConfigRepository failed, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:11:07.701|INFO| N/A||main|c.h.g.c.SentinelLogConfiguration:45|Sentinel日志配置完成 - 已设置为WARNING级别以减少阻塞调用
2025-06-04 16:11:09.215|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [After]
2025-06-04 16:11:09.215|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Before]
2025-06-04 16:11:09.216|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Between]
2025-06-04 16:11:09.216|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Cookie]
2025-06-04 16:11:09.216|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Header]
2025-06-04 16:11:09.216|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Host]
2025-06-04 16:11:09.216|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Method]
2025-06-04 16:11:09.216|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Path]
2025-06-04 16:11:09.216|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Query]
2025-06-04 16:11:09.216|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [ReadBody]
2025-06-04 16:11:09.217|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [RemoteAddr]
2025-06-04 16:11:09.217|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [Weight]
2025-06-04 16:11:09.217|INFO| N/A||main|o.s.c.g.r.RouteDefinitionRouteLocator:88|Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-06-04 16:11:09.412|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 16:11:09.479|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 16:11:09.550|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 16:11:09.604|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 16:11:09.648|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 16:11:09.685|WARN| N/A||main|o.s.c.o.FeignClientFactoryBean:386|The provided URL is empty. Will try picking an instance via load-balancing.
2025-06-04 16:11:09.894|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 16:11:09.894|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 16:11:09.904|WARN| N/A||main|c.n.c.sources.URLConfigurationSource:121|No URLs will be polled as dynamic configuration sources.
2025-06-04 16:11:09.904|INFO| N/A||main|c.n.c.sources.URLConfigurationSource:122|To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-06-04 16:11:10.332|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:110|[Sentinel SpringCloudGateway] using AnonymousBlockRequestHandler, responseStatus: 200, responseBody: {"code": 999001, "msg": "当前处于用户访问高峰期，请重新刷新页面！"}
2025-06-04 16:11:10.335|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:143|[Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-06-04 16:11:10.500|WARN| N/A||main|o.s.boot.actuate.endpoint.EndpointId:155|Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-06-04 16:11:10.649|INFO| N/A||main|o.s.b.a.e.web.EndpointLinksResolver:58|Exposing 21 endpoint(s) beneath base path '/actuator'
2025-06-04 16:11:10.761|INFO| N/A||main|c.a.c.s.g.s.SentinelSCGAutoConfiguration:133|[Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-06-04 16:11:11.234|WARN| N/A||main|o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:82|Spring Cloud LoadBalancer is currently working with the default cache. You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-06-04 16:11:11.399|WARN| N/A||main|c.a.c.s.d.c.SentinelConverter:80|converter can not convert rules because source is empty
2025-06-04 16:11:11.673|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 16 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:11:11.859|INFO| N/A||main|o.s.b.w.e.netty.NettyWebServer:111|Netty started on port 9000
2025-06-04 16:11:12.156|INFO| N/A||main|cn.hydee.gateway.GateWayApplication:61|Started GateWayApplication in 56.258 seconds (JVM running for 58.951)
2025-06-04 16:11:12.158|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:113|grey application started!
2025-06-04 16:11:12.158|INFO| N/A||main|c.h.s.g.s.lib.smooth.SmoothService:115|grey registry success!
2025-06-04 16:11:12.162|INFO| N/A||main|DataLoader:59|#$$加载接口、应用数据开始
2025-06-04 16:11:12.224|WARN| N/A||main|o.s.c.l.c.ServiceInstanceListSupplierBuilder:229|LoadBalancerCacheManager not available, returning delegate without caching.
2025-06-04 16:11:51.633|ERROR| N/A||reactor-http-nio-2|c.h.g.filter.AccessGatewayFilter:181|权限校验过程中发生异常
reactor.blockhound.BlockingOperationError: Blocking call! sun.misc.Unsafe#park
	at sun.misc.Unsafe.park(Unsafe.java)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxUsingWhen] :
	reactor.core.publisher.Flux.usingWhen(Flux.java:1936)
	org.springframework.data.redis.core.ReactiveRedisTemplate.doInConnection(ReactiveRedisTemplate.java:195)
Error has been observed at the following site(s):
	|_      Flux.usingWhen ⇢ at org.springframework.data.redis.core.ReactiveRedisTemplate.doInConnection(ReactiveRedisTemplate.java:195)
	|_           Mono.from ⇢ at org.springframework.data.redis.core.ReactiveRedisTemplate.createMono(ReactiveRedisTemplate.java:179)
	|_            Mono.map ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.getJWTUserReactive(AccessGatewayFilter.java:283)
	|_ Mono.defaultIfEmpty ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.getJWTUserReactive(AccessGatewayFilter.java:292)
	|_        Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.filter(AccessGatewayFilter.java:125)
Stack trace:
		at sun.misc.Unsafe.park(Unsafe.java)
		at java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
		at java.util.concurrent.CompletableFuture$Signaller.block(CompletableFuture.java:1693)
		at java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3323)
		at java.util.concurrent.CompletableFuture.waitingGet(CompletableFuture.java:1729)
		at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1895)
		at io.lettuce.core.cluster.RedisClusterClient.get(RedisClusterClient.java:883)
		at io.lettuce.core.cluster.RedisClusterClient.getPartitions(RedisClusterClient.java:321)
		at org.springframework.data.redis.connection.lettuce.ClusterConnectionProvider.getConnectionAsync(ClusterConnectionProvider.java:92)
		at org.springframework.data.redis.connection.lettuce.ClusterConnectionProvider.getConnectionAsync(ClusterConnectionProvider.java:40)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionProvider.getConnection(LettuceConnectionProvider.java:53)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1459)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1247)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1230)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedReactiveConnection(LettuceConnectionFactory.java:989)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveClusterConnection(LettuceConnectionFactory.java:463)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:441)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:98)
		at org.springframework.data.redis.core.ReactiveRedisTemplate.lambda$doInConnection$0(ReactiveRedisTemplate.java:198)
		at reactor.core.publisher.MonoSupplier.call(MonoSupplier.java:85)
		at reactor.core.publisher.FluxUsingWhen.subscribe(FluxUsingWhen.java:80)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at com.alibaba.csp.sentinel.adapter.reactor.MonoSentinelOperator.subscribe(MonoSentinelOperator.java:40)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:255)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:157)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:73)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:281)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:860)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:73)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1815)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:151)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:281)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:860)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1815)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:149)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2397)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onSubscribe(MonoFilterWhen.java:112)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:448)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onNext(FluxConcatMap.java:250)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:98)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:44)
		at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:270)
		at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:228)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.request(FluxDematerialize.java:127)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:235)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onSubscribe(FluxDematerialize.java:77)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:448)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:218)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:255)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:915)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:654)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:478)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:526)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:94)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:209)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:311)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:432)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
2025-06-04 16:11:54.438|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 32 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:13:08.951|ERROR| N/A||reactor-http-nio-2|c.h.g.filter.AccessGatewayFilter:181|权限校验过程中发生异常
reactor.blockhound.BlockingOperationError: Blocking call! sun.misc.Unsafe#park
	at sun.misc.Unsafe.park(Unsafe.java)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxUsingWhen] :
	reactor.core.publisher.Flux.usingWhen(Flux.java:1936)
	org.springframework.data.redis.core.ReactiveRedisTemplate.doInConnection(ReactiveRedisTemplate.java:195)
Error has been observed at the following site(s):
	|_      Flux.usingWhen ⇢ at org.springframework.data.redis.core.ReactiveRedisTemplate.doInConnection(ReactiveRedisTemplate.java:195)
	|_           Mono.from ⇢ at org.springframework.data.redis.core.ReactiveRedisTemplate.createMono(ReactiveRedisTemplate.java:179)
	|_            Mono.map ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.getJWTUserReactive(AccessGatewayFilter.java:283)
	|_ Mono.defaultIfEmpty ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.getJWTUserReactive(AccessGatewayFilter.java:292)
	|_        Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.filter(AccessGatewayFilter.java:125)
Stack trace:
		at sun.misc.Unsafe.park(Unsafe.java)
		at java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
		at java.util.concurrent.CompletableFuture$Signaller.block(CompletableFuture.java:1693)
		at java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3323)
		at java.util.concurrent.CompletableFuture.waitingGet(CompletableFuture.java:1729)
		at java.util.concurrent.CompletableFuture.join(CompletableFuture.java:1934)
		at org.springframework.data.redis.connection.lettuce.LettuceFutureUtils.join(LettuceFutureUtils.java:68)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionProvider.getConnection(LettuceConnectionProvider.java:53)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1459)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1247)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1230)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedReactiveConnection(LettuceConnectionFactory.java:989)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveClusterConnection(LettuceConnectionFactory.java:463)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:441)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:98)
		at org.springframework.data.redis.core.ReactiveRedisTemplate.lambda$doInConnection$0(ReactiveRedisTemplate.java:198)
		at reactor.core.publisher.MonoSupplier.call(MonoSupplier.java:85)
		at reactor.core.publisher.FluxUsingWhen.subscribe(FluxUsingWhen.java:80)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at com.alibaba.csp.sentinel.adapter.reactor.MonoSentinelOperator.subscribe(MonoSentinelOperator.java:40)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:255)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:157)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:73)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:281)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:860)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:73)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1815)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:151)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:281)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:860)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1815)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:149)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2397)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onSubscribe(MonoFilterWhen.java:112)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:448)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onNext(FluxConcatMap.java:250)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:98)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:44)
		at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:270)
		at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:228)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.request(FluxDematerialize.java:127)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:235)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onSubscribe(FluxDematerialize.java:77)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:448)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:218)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:255)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:915)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:654)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:478)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:526)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:94)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:209)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:311)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:432)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
2025-06-04 16:13:11.710|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 64 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
2025-06-04 16:14:33.302|ERROR| N/A||reactor-http-nio-3|c.h.g.filter.AccessGatewayFilter:181|权限校验过程中发生异常
reactor.blockhound.BlockingOperationError: Blocking call! sun.misc.Unsafe#park
	at sun.misc.Unsafe.park(Unsafe.java)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Assembly trace from producer [reactor.core.publisher.FluxUsingWhen] :
	reactor.core.publisher.Flux.usingWhen(Flux.java:1936)
	org.springframework.data.redis.core.ReactiveRedisTemplate.doInConnection(ReactiveRedisTemplate.java:195)
Error has been observed at the following site(s):
	|_      Flux.usingWhen ⇢ at org.springframework.data.redis.core.ReactiveRedisTemplate.doInConnection(ReactiveRedisTemplate.java:195)
	|_           Mono.from ⇢ at org.springframework.data.redis.core.ReactiveRedisTemplate.createMono(ReactiveRedisTemplate.java:179)
	|_            Mono.map ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.getJWTUserReactive(AccessGatewayFilter.java:283)
	|_ Mono.defaultIfEmpty ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.getJWTUserReactive(AccessGatewayFilter.java:292)
	|_        Mono.flatMap ⇢ at cn.hydee.gateway.filter.AccessGatewayFilter.filter(AccessGatewayFilter.java:125)
Stack trace:
		at sun.misc.Unsafe.park(Unsafe.java)
		at java.util.concurrent.locks.LockSupport.park(LockSupport.java:175)
		at java.util.concurrent.CompletableFuture$Signaller.block(CompletableFuture.java:1693)
		at java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3323)
		at java.util.concurrent.CompletableFuture.waitingGet(CompletableFuture.java:1729)
		at java.util.concurrent.CompletableFuture.join(CompletableFuture.java:1934)
		at org.springframework.data.redis.connection.lettuce.LettuceFutureUtils.join(LettuceFutureUtils.java:68)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionProvider.getConnection(LettuceConnectionProvider.java:53)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.getConnection(LettuceConnectionFactory.java:1459)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getNativeConnection(LettuceConnectionFactory.java:1247)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$SharedConnection.getConnection(LettuceConnectionFactory.java:1230)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getSharedReactiveConnection(LettuceConnectionFactory.java:989)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveClusterConnection(LettuceConnectionFactory.java:463)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:441)
		at org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.getReactiveConnection(LettuceConnectionFactory.java:98)
		at org.springframework.data.redis.core.ReactiveRedisTemplate.lambda$doInConnection$0(ReactiveRedisTemplate.java:198)
		at reactor.core.publisher.MonoSupplier.call(MonoSupplier.java:85)
		at reactor.core.publisher.FluxUsingWhen.subscribe(FluxUsingWhen.java:80)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at com.alibaba.csp.sentinel.adapter.reactor.MonoSentinelOperator.subscribe(MonoSentinelOperator.java:40)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:255)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:157)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:73)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:281)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:860)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:73)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1815)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:151)
		at reactor.core.publisher.FluxMapFuseable$MapFuseableSubscriber.onNext(FluxMapFuseable.java:127)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:281)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:860)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1815)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:149)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2397)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onSubscribe(MonoFilterWhen.java:112)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:448)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onNext(FluxConcatMap.java:250)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:98)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:44)
		at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:270)
		at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:228)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.request(FluxDematerialize.java:127)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:235)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onSubscribe(FluxDematerialize.java:77)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:448)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:218)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:164)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4150)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:255)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:915)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:654)
		at reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:478)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:526)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:94)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:209)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:311)
		at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:432)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:276)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:748)
2025-06-04 16:14:37.331|WARN| N/A||Apollo-RemoteConfigLongPollService-1|c.c.f.a.i.RemoteConfigLongPollService:197|Long polling failed, will retry in 120 seconds. appId: businesses-gateway, cluster: default, namespaces: application.yml+application, long polling url: null, reason: Get config services failed from http://*************:9100/services/config?appId=businesses-gateway&ip=********** [Cause: Could not complete get operation [Cause: connect timed out]]
