package cn.hydee.gateway.config;

import cn.hydee.gateway.dto.ResourceDTO;
import cn.hydee.gateway.dto.ResponseBase;
import cn.hydee.gateway.service.ResourceClient;
import cn.hydee.gateway.util.Const;
import io.micrometer.core.instrument.util.StringUtils;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;

/**
 * <AUTHOR>
 */
//@Component
@Slf4j
public class GatewayApplicationRunner implements ApplicationRunner {

    @Value("${api.base-info-version}")
    private String baseInfoVersion;

    @Autowired
    private ResourceClient resourceClient;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void run(ApplicationArguments args) {
        // 请求业务中心，加载所有有效资源
        ResponseBase<List<ResourceDTO>> base = resourceClient.loadAllNeedPayRes(baseInfoVersion);
        if (base != null && base.checkSuccess()) {
//            log.info("加载所有有效资源数量：{}",base.getData().size());
            List<ResourceDTO> data = base.getData();
            Set<String> set = new HashSet<>();
            data.forEach(resourceDTO -> {
                if (!StringUtils.isEmpty(resourceDTO.getReMethod()) &&
                        !StringUtils.isEmpty(resourceDTO.getRePath())) {
                    String path = resourceDTO.getReMethod() + resourceDTO.getRePath();
                    set.add(path);
                }
            });
            if (!set.isEmpty()) {
                redisTemplate.delete(Const.NEED_PAY_RES_KEY);
                SetOperations<String, Object> opsForSet = redisTemplate.opsForSet();
                Long add = opsForSet.add(Const.NEED_PAY_RES_KEY, set.toArray());
//                log.info("系统收费资源存储redis结果：{}",add);
            }
        } else {
            log.error("网关启动异常，获取有效资源失败");
        }
    }
}
