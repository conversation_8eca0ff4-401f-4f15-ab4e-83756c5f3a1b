package cn.hydee.gateway.service;

import cn.hydee.gateway.dto.ResourceDTO;
import cn.hydee.gateway.dto.ResponseBase;
import cn.hydee.gateway.util.Const;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * 资源服务请求
 * <AUTHOR>
 */
@FeignClient(name = Const.SERVICE_BASEINFO)
public interface ResourceClient {

    @GetMapping("/{version}/res/all-d/0")
    ResponseBase<List<ResourceDTO>> loadAllNeedPayRes(@PathVariable String version);
}
