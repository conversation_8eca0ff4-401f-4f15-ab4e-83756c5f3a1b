package cn.hydee.gateway.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 路由配置
 * <AUTHOR>
 * @version 1.0
 * @date 2019/9/24 10:20
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "gate.ignore")
public class ExcludeUrlConfig {
    private List<String> startWith;
    private List<String> endWith;
    private List<String> logEnd;

    /**
     * URI是否以什么开头
     * @param requestUri 访问路径
     * @return true 路径满足当前配置的开头
     */
    public boolean isStartWith(String requestUri) {
        for (String s : startWith) {
            if (!s.isEmpty() && requestUri.startsWith(s)) {
                return true;
            }
        }
        return false;
    }

    /**
     * URI是否以什么结束
     * @param requestUri 访问路径
     * @return true 路径满足当前配置的开头
     */
    public boolean isEndWith(String requestUri) {
        for (String s : endWith) {
            if (!s.isEmpty() && requestUri.endsWith(s)) {
                return true;
            }
        }
        return false;
    }

    /**
     * URI是否以什么开头
     * @param requestUri 访问路径
     * @return true 路径满足当前配置的开头
     */
    public boolean isLogEndWith(String requestUri) {
        for (String s : logEnd) {
            if (!s.isEmpty() && requestUri.endsWith(s)) {
                return true;
            }
        }
        return false;
    }
}
