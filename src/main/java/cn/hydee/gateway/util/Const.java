package cn.hydee.gateway.util;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/6/21 11:05
 */
public class Const {
    public final static String NEED_PAY_RES_KEY = "need_pay_res_key";
    public final static String SERVICE_BASEINFO = "hydee-middle-baseinfo";

    public final static String METHOD_POST = "POST";
    public final static String CODE_REPEAT_MSG = "您的操作过于频繁，请稍后重试";

    public static final String USER_ZH_NAME_KEY = "baseinfo-user-zh-name";

    /**
     * 登录的有效用户ID
     */
    public static final String EFFECTIVE_LOGIN_USER_ID = "SYSTEM_EFFECTIVE_LOGIN_USER_ID";

    /**
     * 登录鉴权token缓存key
     */
    public static final String REDIS_OAUTH_LOGIN_TOKEN_KEY = "OAUTH:LOGIN:TOKEN:";

    /**
     * CookieRedis前缀
     */
    public static final String REDIS_BASE_PREFIX = "yxt:login:token:";
}
