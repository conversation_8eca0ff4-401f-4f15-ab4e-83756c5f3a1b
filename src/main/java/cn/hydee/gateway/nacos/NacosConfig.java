package cn.hydee.gateway.nacos;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * TODO
 *
 * @version 1.0.0
 * @acutor min
 * @date 2023/5/16 10:55
 **/
@ConfigurationProperties("spring.cloud.nacos.discovery")
@Configuration
public class NacosConfig {

    private String serverAddr;

    private String namespace;

    public String getServerAddr() {
        return serverAddr;
    }

    public void setServerAddr(String serverAddr) {
        this.serverAddr = serverAddr;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }
}
