package cn.hydee.gateway.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年05月14日 14:04
 * @email: <EMAIL>
 */
@SuppressWarnings({"unused", "SpringFacetCodeInspection"})
@Configuration
@ConfigurationProperties("route-forbidden-config")
@Data
public class RouteForbiddenConfig {

  private List<String> urlList;

  public Boolean containUrl(String urlPath) {
    if (CollectionUtils.isEmpty(this.urlList)) {
      return Boolean.FALSE;
    }
    for (String url : this.urlList) {
      if (urlPath.contains(url)) {
        return Boolean.TRUE;
      }
    }
    return Boolean.FALSE;
  }
}
