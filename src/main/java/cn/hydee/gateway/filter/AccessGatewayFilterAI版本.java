package cn.hydee.gateway.filter;

import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_ORIGINAL_REQUEST_URL_ATTR;

import cn.hydee.gateway.config.ExcludeUrlConfig;
import cn.hydee.gateway.config.UserAuthConfig;
import cn.hydee.gateway.constants.CommonConstants;
import cn.hydee.gateway.domain.JWTInfo;
import cn.hydee.gateway.domain.ReturnData;
import cn.hydee.gateway.domain.TokenForbiddenResponse;
import cn.hydee.gateway.domain.UrlForbiddenResponse;
import cn.hydee.gateway.filter.context.UserInfoContext;
import cn.hydee.gateway.util.Const;
import cn.hydee.gateway.util.UserAuthUtil;
import com.alibaba.fastjson.JSONObject;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.ReactiveHashOperations;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * 权限校验过滤器
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(ExcludeUrlConfig.class)
public class AccessGatewayFilterAI版本 extends AbstractGatewayFilter {

  private static final String URL_SPLIT = "/";
  private static final String PATH_VARIABLE_SPLIT = "/{";
  protected final static String HEAD_USER_ID_KEY = "userId";
  private final static String HEAD_USER_NAME_KEY = "userName";
  /**
   * 用户中文名
   */
  private final static String HEAD_USER_ZH_NAME_KEY = "userZhName";
  protected final static String HEAD_USER_MERCODE = "merCode";
  /**
   * 员工编码标识
   */
  public static final String HEAD_EMP_CODE = "empCode";
  /**
   * 账号类型标识
   */
  public static final String HEAD_ACC_TYPE = "accType";
  private static final String GATE_WAY_PREFIX = "/api";
  //针对哪些商户放行
  @Value("${token.resolver.igrone.mercodes:'SPHYDEE,hydee'}")
  private String HEAD_PASS_MERCODES = "SPHYDEE";

  @Autowired
  private ExcludeUrlConfig excludeUrlConfig;

  @Autowired
  private UserAuthConfig userAuthConfig;

  @Autowired
  private UserAuthUtil userAuthUtil;


  @Resource
  private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;


  @Override
  public Mono<Void> filter(ServerWebExchange serverWebExchange,
      GatewayFilterChain gatewayFilterChain) {
    // 如果不重新校验,则直接放行
    if (Boolean.FALSE.equals(
        serverWebExchange.getAttributes().get(YxtLoginAccessGatewayFilterFactory.RE_CHECK_TOKEN))) {
      return gatewayFilterChain.filter(serverWebExchange);
    }

    // 1. 处理requestUri
    serverWebExchange.getAttributes()
        .computeIfAbsent(GATEWAY_ORIGINAL_REQUEST_URL_ATTR, s -> new LinkedHashSet<>());
    LinkedHashSet requiredAttribute = serverWebExchange.getRequiredAttribute(
        GATEWAY_ORIGINAL_REQUEST_URL_ATTR);
    ServerHttpRequest request = serverWebExchange.getRequest();
    String requestUri = request.getPath().pathWithinApplication().value();
    String method = request.getMethodValue();
    Iterator<URI> iterator = requiredAttribute.iterator();
    while (iterator.hasNext()) {
      URI next = iterator.next();
      if (next.getPath().startsWith(GATE_WAY_PREFIX)) {
        requestUri = next.getPath().substring(GATE_WAY_PREFIX.length());
      }
    }

    // 将变量声明为final，以便在lambda表达式中使用
    final String finalRequestUri = requestUri;
    final String finalMethod = method;

    // 2. 从Token中解析用户信息（响应式处理）
    return getJWTUserReactive(request)
        .flatMap(userInfo -> {
          ServerHttpRequest.Builder mutate = request.mutate();
          JWTInfo user = userInfo.getUser();

          // 设置请求头信息
          if (user != null) {
            setUserHeaders(mutate, user);
          }

          // 3. 不进行登录拦截的地址
          if (excludeUrlConfig.isStartWith(finalRequestUri) || excludeUrlConfig.isEndWith(
              finalRequestUri)) {
            return permit(serverWebExchange, gatewayFilterChain);
          }

          // 4. 需要登录，判断用户token有效性
          if (user == null) {
            return forbiddenResponse(serverWebExchange,
                new TokenForbiddenResponse("User Token Forbidden or Expired!"));
          }

          // 5. 检查用户是否在有效登录用户集合中（响应式）
          return reactiveRedisTemplate.opsForSet()
              .isMember(Const.EFFECTIVE_LOGIN_USER_ID, user.getUserId())
              .flatMap(isMember -> {
                if (!isMember) {
                  return forbiddenResponse(serverWebExchange,
                      new TokenForbiddenResponse("User Token Forbidden or Expired!"));
                }

                // 6. 判断链接是否收费，不收费放行（响应式）
                return matchNeedPayPathReactive(finalMethod, finalRequestUri)
                    .flatMap(matchPath -> {
                      if (StringUtils.isEmpty(matchPath)) {
                        return permit(serverWebExchange, gatewayFilterChain);
                      }

                      // 7. 判断资源是否有效（响应式）
                      return checkUserPermissionReactive(user.getUserId(), matchPath)
                          .flatMap(hasPermission -> {
                            if (!hasPermission) {
                              return forbiddenResponse(serverWebExchange,
                                  new UrlForbiddenResponse(
                                      "User Forbidden!Does not has Permission!"));
                            }

                            // 8. 重新构建Request,返回
                            ServerHttpRequest build = mutate.build();
                            return gatewayFilterChain.filter(
                                serverWebExchange.mutate().request(build).build());
                          });
                    });
              });
        })
        .onErrorResume(error -> {
          log.error("权限校验过程中发生异常", error);
          return forbiddenResponse(serverWebExchange,
              new TokenForbiddenResponse("Authentication failed due to system error!"));
        });
  }

  private Mono<Void> permit(ServerWebExchange serverWebExchange,
      GatewayFilterChain gatewayFilterChain) {
    ServerHttpRequest request = serverWebExchange.getRequest();
    String requestUri = request.getPath().pathWithinApplication().value();
    if (excludeUrlConfig.isLogEndWith(requestUri) && Const.METHOD_POST.equals(
        request.getMethodValue())) {
      // 打印用户登录信息,从请求里获取Post请求体
      ServerHttpRequestDecorator decorator = new HyDeeRequestDecorator(request);
      return gatewayFilterChain.filter(serverWebExchange.mutate().request(decorator).build());
    }
    return gatewayFilterChain.filter(serverWebExchange.mutate().request(request).build());
  }

  @Override
  public int getOrder() {
    return FilterOrder.AccessGatewayFilter;
  }

  /**
   * 响应式校验用户权限 注意：由于原有redisTemplate使用Jackson序列化存储Date对象， 而reactiveRedisTemplate使用字符串序列化，需要兼容处理两种格式
   *
   * @param userId    用户ID
   * @param matchPath 匹配上的路径
   * @return Mono<Boolean> true 有权限，false-无权限
   */
  private Mono<Boolean> checkUserPermissionReactive(String userId, String matchPath) {
    return reactiveRedisTemplate.opsForHash()
        .get(CommonConstants.REDIS_PERMISSION_KEY + userId, matchPath)
        .cast(Date.class) // 将Object类型转换为Date类型
        .map(date -> {
          try {
            return System.currentTimeMillis() <= date.getTime();
          } catch (Exception e) {
            log.warn("权限时间数据解析失败: userId={}, matchPath={}, dateStr={}",
                userId, matchPath, date, e);
            return false;
          }
        })
        .defaultIfEmpty(false) // 如果没有找到权限记录，默认返回false
        .doOnError(
            error -> log.error("权限校验异常: userId={}, matchPath={}", userId, matchPath, error));
  }


  /**
   * 网关抛异常
   *
   * @param body 返回的数据
   */
  private Mono<Void> forbiddenResponse(ServerWebExchange serverWebExchange, ReturnData body) {
    serverWebExchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
    byte[] bytes = JSONObject.toJSONString(body).getBytes(StandardCharsets.UTF_8);
    DataBuffer buffer = serverWebExchange.getResponse().bufferFactory().wrap(bytes);
    return serverWebExchange.getResponse().writeWith(Flux.just(buffer));
  }


  /**
   * 响应式获取JWT用户信息
   *
   * @param request 请求
   * @return Mono<UserInfo> 用户信息
   */
  private Mono<UserInfoContext> getJWTUserReactive(ServerHttpRequest request) {
    try {
      // 获取认证token
      String authToken = request.getHeaders().getFirst(userAuthConfig.getTokenHeader());
      if (StringUtils.isBlank(authToken)) {
        authToken = request.getQueryParams().getFirst("token");
      }
      if (authToken == null) {
        return Mono.just(new UserInfoContext(null, null));
      }

      // 解析token获取用户信息
      JWTInfo info = userAuthUtil.getInfoFromToken(authToken);
      if (info == null) {
        return Mono.just(new UserInfoContext(null, authToken));
      }

      String multiLogin = info.getMultiLogin();
      String userId = info.getUserId();
      final String finalAuthToken = authToken;

      // 判断用户是否多端登录，如果非多端登录，需要验证当前用户是否已经登录的缓存key是否和当前token一致
      if (Objects.equals(multiLogin, Boolean.FALSE.toString())) {
        return reactiveRedisTemplate.opsForValue()
            .get(Const.REDIS_OAUTH_LOGIN_TOKEN_KEY + userId)
            .map(loginTokenCache -> {
              if (loginTokenCache == null) {
                return new UserInfoContext(null, finalAuthToken);
              }
              if (!Objects.equals(finalAuthToken, loginTokenCache)) {
                return new UserInfoContext(null, finalAuthToken);
              }
              return validateMerchantCode(request, info, finalAuthToken);
            })
            .defaultIfEmpty(new UserInfoContext(null, finalAuthToken));
      } else {
        // 多端登录允许，直接验证商户码
        return Mono.just(validateMerchantCode(request, info, finalAuthToken));
      }
    } catch (Exception e) {
      log.warn("解析JWT用户信息异常", e);
      return Mono.just(new UserInfoContext(null, null));
    }
  }

  /**
   * 验证商户码并设置用户中文名缓存
   *
   * @param request   请求
   * @param info      JWT信息
   * @param authToken 认证token
   * @return UserInfo
   */
  private UserInfoContext validateMerchantCode(ServerHttpRequest request, JWTInfo info,
      String authToken) {
    String headerMerCode = request.getHeaders().getFirst(HEAD_USER_MERCODE);
    String tokenMerCode = info.getMerCode();
    log.debug("打印参数据{},{}", headerMerCode, tokenMerCode);

    if (!ObjectUtils.isEmpty(headerMerCode) && !ObjectUtils.isEmpty(tokenMerCode)
        && !headerMerCode.equals(tokenMerCode)) {
      if (!HEAD_PASS_MERCODES.contains(tokenMerCode)) {
        log.debug("商户token信息不匹配{},{}", headerMerCode, tokenMerCode);
        return new UserInfoContext(null, authToken);
      }
    }

    // 设置用户中文名到Redis缓存
    setZhNameToRedis(info);
    return new UserInfoContext(info, authToken);
  }

  /**
   * 设置用户请求头信息
   *
   * @param mutate 请求构建者
   * @param info   JWT用户信息
   */
  private void setUserHeaders(ServerHttpRequest.Builder mutate, JWTInfo info) {
    mutate.headers(httpHeaders -> {
      httpHeaders.set(HEAD_USER_ID_KEY, info.getUserId());
      httpHeaders.set(HEAD_USER_NAME_KEY, info.getUserName());
      httpHeaders.set(HEAD_EMP_CODE, info.getEmpCode());
      try {
        httpHeaders.set(HEAD_USER_ZH_NAME_KEY,
            URLEncoder.encode(info.getZhName(),
                String.valueOf(StandardCharsets.UTF_8)));
      } catch (UnsupportedEncodingException e) {
        throw new RuntimeException(e);
      }
      httpHeaders.set(HEAD_ACC_TYPE,
          info.getAccType() == null ? "" : String.valueOf(info.getAccType()));
    });

    // 登录强制重置header里面的商户号
    String tokenMerCode = info.getMerCode();
    if (!ObjectUtils.isEmpty(tokenMerCode) && !HEAD_PASS_MERCODES.contains(tokenMerCode)) {
      List<String> merCodes = new ArrayList<>();
      merCodes.add(tokenMerCode);
      mutate.headers(httpHeaders -> {
        httpHeaders.put(HEAD_USER_MERCODE, merCodes);
      });
    }
  }


  private void setZhNameToRedis(JWTInfo info) {
    ReactiveHashOperations<String, String, String> hashOperations = reactiveRedisTemplate.opsForHash();
    hashOperations.put(Const.USER_ZH_NAME_KEY, info.getUserName(),
            info.getZhName())
        .subscribeOn(Schedulers.boundedElastic()) // 在独立线程池执行
        .subscribe(
            result -> {
              if (!result) {
                log.warn("用户中文名缓存失败: {}", info.getUserName());
              }
            },
            error -> log.warn(String.format("用户中文名【%s】缓存异常", info.getUserName()),
                error)
        ) //在响应式环境中,订阅才会执行
    ;
  }

  /**
   * 响应式匹配当前资源是否是需要支付的资源
   *
   * @param method HTTP方法
   * @param path   访问路径
   * @return Mono<String> 找到匹配的资源，没有则返回空字符串
   */
  private Mono<String> matchNeedPayPathReactive(String method, String path) {
    return reactiveRedisTemplate.opsForSet()
        .members(Const.NEED_PAY_RES_KEY)
        .collectList()
        .map(members -> {
          if (members == null || members.isEmpty()) {
            return "";
          }
          String fullPath = method + path;
          for (String expect : members) {
            if (isRight(expect, fullPath)) {
              return expect;
            }
          }
          return "";
        })
        .onErrorReturn(""); // 发生异常时返回空字符串，表示不需要付费
  }


  /**
   * 实际访问路径是否与期望路径匹配
   *
   * @param respect 期望路径
   * @param actual  实际路径
   * @return boolean true-匹配，false-不匹配
   * <AUTHOR>
   * @date 15:44 2019/8/15
   **/
  private boolean isRight(String respect, String actual) {
    if (respect.contains(PATH_VARIABLE_SPLIT)) {
      return respect.split(URL_SPLIT).length == actual.split(URL_SPLIT).length && actual.startsWith(
          respect.substring(0, respect.indexOf(PATH_VARIABLE_SPLIT)));
    } else {
      return actual.equals(respect);
    }
  }
}
