package cn.hydee.gateway.dto;

import cn.hydee.gateway.constants.AccountType;
import com.yxt.lang.dto.OperateContext;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;


/**
 * 用于反序列化redis中token聚合根的对象
 * <AUTHOR>
 */
@Slf4j
@Getter
@Setter
@ToString(callSuper = true)
public class TokenDTO {

    /**
     * tokenNo
     */
    private String tokenNo;

    /**
     * token
     */
    private String token;

    /**
     * 用户名
     */
    private String userName;
    /**
     * 中文名
     */
    private String zhName;

    /**
     * 商户号
     */
    private String merCode;

    /**
     * userId
     */
    private String userId;

    /**
     * 失效时间
     */
    private Integer expire;

    /**
     * 员工编码,员工账号才会不为空，其他账号类型可能为空
     */
    private String empCode;

    /**
     * 账号类型 0:员工账号 1：门店账号
     */
    private AccountType accType;

    /**
     * 是否支持多端登录
     */
    private Boolean multiLogin = Boolean.FALSE;

    /**
     * token过期时间戳
     */
    private Long expireTimeStamp;

    /**
     * token过期剩余天数
     */
    private Long expireRemainDay;

    /***
     * LoginIp
     */
    private String LoginIp;

    /**
     * 客户端唯一标识
     */
    private String clientId;

    private OperateContext operateContext;

    /**
     * 登录时间
     */
    private Date loginTime;

}
