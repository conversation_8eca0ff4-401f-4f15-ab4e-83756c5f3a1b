//package cn.hydee.gateway.config;
//
//import cn.hydee.gateway.nacos.Cache;
//import cn.hydee.gateway.nacos.NacosConfig;
//import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
//import com.alibaba.csp.sentinel.Env;
//import com.alibaba.csp.sentinel.Sph;
//import com.alibaba.csp.sentinel.adapter.gateway.sc.SentinelGatewayFilter;
//import com.alibaba.csp.sentinel.adapter.gateway.sc.callback.DefaultBlockRequestHandler;
//import com.alibaba.csp.sentinel.adapter.gateway.sc.callback.GatewayCallbackManager;
//import com.alibaba.csp.sentinel.adapter.gateway.sc.exception.SentinelGatewayBlockExceptionHandler;
//import com.alibaba.csp.sentinel.command.CommandCenterProvider;
//import com.alibaba.csp.sentinel.slots.block.BlockException;
//import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
//import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
//import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRuleManager;
//import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
//import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
//import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
//import com.alibaba.csp.sentinel.slots.system.SystemBlockException;
//import com.alibaba.csp.sentinel.slots.system.SystemRule;
//import com.alibaba.csp.sentinel.slots.system.SystemRuleManager;
//import com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter;
//import com.alibaba.csp.sentinel.transport.config.TransportConfig;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.nacos.api.PropertyKeyConst;
//import com.alibaba.nacos.api.config.ConfigService;
//import com.alibaba.nacos.api.config.listener.Listener;
//import com.google.common.collect.Maps;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.ObjectProvider;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.cloud.gateway.filter.GlobalFilter;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.Ordered;
//import org.springframework.core.annotation.Order;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.MediaType;
//import org.springframework.http.codec.ServerCodecConfigurer;
//import org.springframework.web.reactive.function.server.ServerResponse;
//import org.springframework.web.reactive.result.view.ViewResolver;
//import org.springframework.web.server.ServerWebExchange;
//import reactor.core.publisher.Mono;
//
//import javax.annotation.PostConstruct;
//import java.lang.reflect.Field;
//import java.nio.charset.StandardCharsets;
//import java.util.*;
//import java.util.concurrent.Executor;
//import java.util.stream.Collectors;
//
//import static org.springframework.web.reactive.function.BodyInserters.fromObject;
//
//@Setter
//@Getter
//@Configuration
//@Slf4j
//@ConfigurationProperties(prefix = "sentinel.zookeeper")
//@ConditionalOnProperty(prefix = "sentinel.zookeeper", name = "enable", matchIfMissing = true)
//public class SentinelConfiguration {
//
//    @Value("${spring.application.name}")
//    private String applicationName;
//
//    private String zookeeperConnectString;
//
//    private String zookeeperBasePath = "/sentinel";
//
//
//    @Value("${sentinel.flow.tip-message:}")
//    private String sentinelFlowTipMessage;
//
//    @Value("${sentinel.degrade.tip-message:}")
//    private String sentinelDegradeTipMessage;
//
//    @Value("${sentinel.system.tip-message:}")
//    private String sentinelSystemTipMessage;
//
//    private Integer responseErrorCode = 10003;
//
//    private String defaultResponseErrorMessage = "抱歉, 当前请求数量过多，请稍后重试";
//
//    private final List<ViewResolver> viewResolvers;
//
//    private final ServerCodecConfigurer serverCodecConfigurer;
//
//    public SentinelConfiguration(ObjectProvider<List<ViewResolver>> viewResolversProvider,
//                                 ServerCodecConfigurer serverCodecConfigurer) {
//        this.viewResolvers = viewResolversProvider.getIfAvailable(Collections::emptyList);
//        this.serverCodecConfigurer = serverCodecConfigurer;
//    }
//
////    @Bean
////    @Order(Ordered.HIGHEST_PRECEDENCE)
////    public SentinelGatewayBlockExceptionHandler sentinelGatewayBlockExceptionHandler() {
////        // Register the block exception handler for Spring Cloud Gateway.
////        return new SentinelGatewayBlockExceptionHandler(viewResolvers, serverCodecConfigurer);
////    }
//
//    @Bean
//    public SentinelRulesListener buildSentinelRulesListener() {
//        return new SentinelRulesListener();
//    }
//
//    @Autowired(required = false)
//    public void setZookeeperMeta(
//            NacosDiscoveryProperties nacosDiscoveryProperties
//    ) {
//        if (nacosDiscoveryProperties == null) {
//            return;
//        }
//        Map<String, String> meta = nacosDiscoveryProperties.getMetadata();
//        if (meta == null) {
//            return;
//        }
//        SimpleHttpCommandCenter simpleHttpCommandCenter = (SimpleHttpCommandCenter) CommandCenterProvider.getCommandCenter();
//        Sph sph = Env.sph;
//        log.info("InitExecutor.doInit.... {}", sph.getClass().getName());
//        try {
//            Field field = SimpleHttpCommandCenter.class.getDeclaredField("socketReference");
//            field.setAccessible(true);
//            int tryCount = 0;
//            while (field.get(simpleHttpCommandCenter) == null) {
//                Thread.sleep(1000);
//                tryCount ++;
//                if (tryCount <= 10) {
//                    continue;
//                }
//                RuntimeException exception = new RuntimeException("sentinel serverSocket start failed!");
//                log.error(exception.getMessage(), exception);
//                throw exception;
//            }
//        } catch (NoSuchFieldException | IllegalAccessException e) {
//            log.error(e.getMessage(), e);
//        } catch (InterruptedException  e) {
//            Thread.currentThread().interrupt();
//        }
//        meta.put("sentinel.port", TransportConfig.getRuntimePort() + "");
//    }
//
//
//    @Bean
//    @Order(-1)
//    public GlobalFilter sentinelGatewayFilter() {
//        return new SentinelGatewayFilter();
//    }
//
//    @PostConstruct
//    public void doInit() {
//        /*
//         * Set MyBlockRequestHandler
//         */
//        GatewayCallbackManager.setBlockHandler(new DefaultBlockRequestHandler(){
//            @Override
//            public Mono<ServerResponse> handleRequest(ServerWebExchange exchange, Throwable ex) {
//                Map<String, Object> body = Maps.newHashMap();
//                String tipMessage = defaultResponseErrorMessage;
//
//                if (ex instanceof BlockException) {
//                    if (ex instanceof FlowException && StringUtils.isNotBlank(sentinelFlowTipMessage)) {
//                        tipMessage = sentinelFlowTipMessage;
//                    }
//                    if (ex instanceof DegradeException && StringUtils.isNotBlank(sentinelDegradeTipMessage)) {
//                        tipMessage = sentinelDegradeTipMessage;
//                    }
//                    if (ex instanceof SystemBlockException && StringUtils.isNotBlank(sentinelSystemTipMessage)) {
//                        tipMessage = sentinelSystemTipMessage;
//                    }
//                }
//
//                body.put("code", responseErrorCode);
//                body.put("msg", tipMessage);
//                return ServerResponse.status(HttpStatus.OK)
//                        .contentType(MediaType.APPLICATION_JSON_UTF8)
//                        .body(fromObject(body));
//            }
//        });
//
//    }
//
//
//    /**
//     * watching zookeeper node, if rules is modified, reload rules.
//     *
//     * Created by peng on 19/12/5.
//     */
//    @Slf4j
//    public static class SentinelRulesListener {
//
//        private static final String BASE_PATH = "sentinel";
//
//        @Autowired
//        private NacosConfig nacosConfig;
//
//        @Value("${spring.application.name}")
//        private String applicationName;
//
//        @PostConstruct
//        public void init() throws Exception {
//
//            Properties properties = new Properties();
//            String url = nacosConfig.getServerAddr();
//            //处理测试环境自己搭建的nacos原生bug
//            if(nacosConfig.getServerAddr().endsWith(";") || nacosConfig.getServerAddr().endsWith("；")){
//                url = nacosConfig.getServerAddr().substring(0,nacosConfig.getServerAddr().length() - 1);
//            }
//            properties.setProperty(PropertyKeyConst.SERVER_ADDR, url);
//            properties.setProperty(PropertyKeyConst.NAMESPACE, nacosConfig.getNamespace());
//            ConfigService configService = Cache.getNacosConfigClient(properties);
////
////            String path = BASE_PATH + "/" + applicationName;
////            NodeCache nodeCache = new NodeCache(client, path, false);
////            nodeCache.start();
//            String sentinelConfig = configService.getConfigAndSignListener(applicationName+"_"+BASE_PATH, "SENTINEL_GROUP", 30000, new Listener() {
//                @Override
//                public Executor getExecutor() {
//                    return null;
//                }
//
//                @Override
//                public void receiveConfigInfo(String jsonStr) {
//                    try {
//                        log.info("=== sentinelLog === sentinelRules changed! {}", jsonStr);
//
//                        JSONObject ruleMap = JSON.parseObject(jsonStr);
//                        if(ruleMap == null){
//                            return;
//                        }
//                        /*
//                         * Load FlowRules
//                         */
//                        if (ruleMap.containsKey("flow")) {
//                            //noinspection unchecked,rawtypes
//                            List<Map> flowRuleMaps = (List<Map>) ruleMap.get("flow");
//                            if (flowRuleMaps != null && !flowRuleMaps.isEmpty()) {
//                                List<FlowRule> flowRules = flowRuleMaps.stream().map(map -> JSON.parseObject(JSON.toJSONString(map), FlowRule.class)).collect(Collectors.toList());
//                                FlowRuleManager.loadRules(flowRules);
//                            } else {
//                                FlowRuleManager.loadRules(new ArrayList<>());
//                            }
//                        }
//
//                        /*
//                         * Load DegradeRules
//                         */
//                        if (ruleMap.containsKey("degrade")) {
//                            //noinspection unchecked,rawtypes
//                            List<Map> degradeMaps = (List<Map>) ruleMap.get("degrade");
//                            if (degradeMaps != null && !degradeMaps.isEmpty()) {
//                                List<DegradeRule> degradeRules = degradeMaps.stream().map(map -> JSON.parseObject(JSON.toJSONString(map), DegradeRule.class)).collect(Collectors.toList());
//                                DegradeRuleManager.loadRules(degradeRules);
//                            } else {
//                                DegradeRuleManager.loadRules(new ArrayList<>());
//                            }
//                        }
//
//                        /*
//                         * Load SystemRules
//                         */
//                        if (ruleMap.containsKey("system")) {
//                            //noinspection unchecked,rawtypes
//                            List<Map> systemMaps = (List<Map>) ruleMap.get("system");
//                            if (systemMaps != null && !systemMaps.isEmpty()) {
//                                List<SystemRule> systemRules = systemMaps.stream().map(map -> JSON.parseObject(JSON.toJSONString(map), SystemRule.class)).collect(Collectors.toList());
//                                SystemRuleManager.loadRules(systemRules);
//                            } else {
//                                SystemRuleManager.loadRules(new ArrayList<>());
//                            }
//                        }
//                    } catch (Exception e) {
//                        log.error("=== sentinelLog === parse sentinelRules error!", e);
//                    }
//                }
//            });
////          下面是初始化逻辑
//            try {
//                initSentinelConfig(sentinelConfig);
//            } catch (Exception e) {
//                log.error("=== sentinelLog === parse sentinelRules error!", e);
//            }
//        }
//
//        /**
//         * 初始化限流配置
//         * @param sentinelConfig
//         */
//        private void initSentinelConfig(String sentinelConfig) {
//            log.info("=== sentinelLog === init sentinelRules! {}", sentinelConfig);
//
//            JSONObject ruleMap = JSON.parseObject(sentinelConfig);
//
//            if(ruleMap == null){
//                return;
//            }
//            /*
//             * Load FlowRules
//             */
//            if (ruleMap.containsKey("flow")) {
//                //noinspection unchecked,rawtypes
//                List<Map> flowRuleMaps = (List<Map>) ruleMap.get("flow");
//                if (flowRuleMaps != null && !flowRuleMaps.isEmpty()) {
//                    List<FlowRule> flowRules = flowRuleMaps.stream().map(map -> JSON.parseObject(JSON.toJSONString(map), FlowRule.class)).collect(Collectors.toList());
//                    FlowRuleManager.loadRules(flowRules);
//                } else {
//                    FlowRuleManager.loadRules(new ArrayList<>());
//                }
//            }
//
//            /*
//             * Load DegradeRules
//             */
//            if (ruleMap.containsKey("degrade")) {
//                //noinspection unchecked,rawtypes
//                List<Map> degradeMaps = (List<Map>) ruleMap.get("degrade");
//                if (degradeMaps != null && !degradeMaps.isEmpty()) {
//                    List<DegradeRule> degradeRules = degradeMaps.stream().map(map -> JSON.parseObject(JSON.toJSONString(map), DegradeRule.class)).collect(Collectors.toList());
//                    DegradeRuleManager.loadRules(degradeRules);
//                } else {
//                    DegradeRuleManager.loadRules(new ArrayList<>());
//                }
//            }
//
//            /*
//             * Load SystemRules
//             */
//            if (ruleMap.containsKey("system")) {
//                //noinspection unchecked,rawtypes
//                List<Map> systemMaps = (List<Map>) ruleMap.get("system");
//                if (systemMaps != null && !systemMaps.isEmpty()) {
//                    List<SystemRule> systemRules = systemMaps.stream().map(map -> JSON.parseObject(JSON.toJSONString(map), SystemRule.class)).collect(Collectors.toList());
//                    SystemRuleManager.loadRules(systemRules);
//                } else {
//                    SystemRuleManager.loadRules(new ArrayList<>());
//                }
//            }
//        }
//    }
//}
