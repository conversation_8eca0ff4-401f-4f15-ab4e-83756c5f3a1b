package cn.hydee.gateway.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 防重设置
 * <AUTHOR>
 * @version 1.0
 * @date 2019/9/24 10:20
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "idempotent")
public class IdempotentConfig {

    private static final String POST = "POST";

    private List<String> postPath;
    private int expireMill;

    /**
     * URI是否以什么开头
     * @param method 访问方法
     * @param requestUri 访问路径
     * @return true 路径满足当前配置的开头
     */
    public boolean isContainWith(String method,String requestUri) {
        return method.equals(POST) && postPath != null && postPath.contains(requestUri);
    }

}
