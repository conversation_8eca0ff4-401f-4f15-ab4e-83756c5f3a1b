package cn.hydee.gateway.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.net.URI;
import java.net.URISyntaxException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage;
import org.springframework.cloud.gateway.support.BodyInserterContext;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ReactiveHttpOutputMessage;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.HandlerStrategies;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 重写URL参数与Body中商户编码
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-30
 */
@Slf4j
//@Configuration
public class RewriteMerCodeFilter implements GlobalFilter, Ordered {
    protected static final String HEAD_USER_MER_CODE = "merCode";
    protected static final String CONTENT_TYPE = "application/json";

    @Value("${token.resolver.body.enable:true}")
    private boolean resolverBody = true;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String merCode = request.getHeaders().getFirst(HEAD_USER_MER_CODE);

        // 跳过请求头中无商户编码的情况，该商户编码由Token中解析得到
        if (ObjectUtils.isEmpty(merCode)) {
            return chain.filter(exchange);
        }

        // 跳过非Get或Post请求
        HttpMethod method = request.getMethod();
        if (method != HttpMethod.GET && method != HttpMethod.POST) {
            return chain.filter(exchange);
        }

        // 处理Body中请求内容
        if (resolverBody) {
            HttpHeaders headers = new HttpHeaders();
            headers.putAll(exchange.getRequest().getHeaders());
            headers.remove(HttpHeaders.CONTENT_LENGTH);
            CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange, headers);

            ServerRequest serverRequest = ServerRequest.create(exchange, HandlerStrategies.withDefaults().messageReaders());
            Mono<String> modifiedBody = serverRequest.bodyToMono(String.class).flatMap(o -> Mono.just(replaceBodyMerCode(o, merCode)));
            BodyInserter<Mono<String>, ReactiveHttpOutputMessage> bodyInserter = BodyInserters.fromPublisher(modifiedBody, String.class);

            return bodyInserter.insert(outputMessage, new BodyInserterContext())
                    .then(Mono.defer(() -> {
                        RequestDecorator decorator = new RequestDecorator(request, headers, outputMessage.getBody(), merCode);
                        return chain.filter(exchange.mutate().request(decorator).build());
                    }));
        } else {
            return chain.filter(exchange);
        }
    }

    @Override
    public int getOrder() {
        return FilterOrder.RewriteMerCodeFilter;
    }

    /**
     * 替换Body中商户编码
     *
     * @param content 原报文
     * @param merCode 商户编码
     * @return 修改后报文
     */
    private String replaceBodyMerCode(String content, String merCode) {

        // 替换商户编码
        if (!ObjectUtils.isEmpty(content) && content.startsWith("{")) { // 只处理JSONObject，不处理JSONArray
            String key = HEAD_USER_MER_CODE;
            JSONObject jsonObject = JSON.parseObject(content);
            String oldMerCode = (String) jsonObject.get(key);
            if (ObjectUtils.isEmpty(oldMerCode)) {
                key = key.toLowerCase();
                oldMerCode = (String) jsonObject.get(key);
            }
            if (!ObjectUtils.isEmpty(oldMerCode) && !oldMerCode.equals(merCode)) {
                jsonObject.put(key, merCode);
                content = jsonObject.toJSONString();
//                log.warn("请求Body中商户编码[{}]与Token中[{}]值不一致！", oldMerCode, merCode);
            }
        }

        return content;
    }

}

@Slf4j
class RequestDecorator extends ServerHttpRequestDecorator {
    private final HttpHeaders headers;
    private final Flux<DataBuffer> body;
    private final String merCode;
    private URI uri;

    public RequestDecorator(ServerHttpRequest request, HttpHeaders headers, Flux<DataBuffer> body, String merCode) {
        super(request);
        this.headers = headers;
        this.body = body;
        this.merCode = merCode;
    }

    @Override
    public HttpHeaders getHeaders() {
        long contentLength = headers.getContentLength();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.putAll(super.getHeaders());
        if (contentLength > 0) {
            httpHeaders.setContentLength(contentLength);
        } else {
            httpHeaders.set(HttpHeaders.TRANSFER_ENCODING, "chunked");
        }
        return httpHeaders;
    }

    @Override
    public Flux<DataBuffer> getBody() {
        return body;
    }

    @Override
    public URI getURI() {
        if (uri != null) {
            return uri;
        }

        // 处理Url中请求参数
        MultiValueMap<String, String> queryParams = super.getQueryParams();
        String key = RewriteMerCodeFilter.HEAD_USER_MER_CODE;
        String oldMerCode = queryParams.getFirst(key);
        if (ObjectUtils.isEmpty(oldMerCode)) {
            key = key.toLowerCase();
            oldMerCode = queryParams.getFirst(key);
        }

        uri = super.getURI();
        if (!ObjectUtils.isEmpty(oldMerCode) && !oldMerCode.equals(merCode)) {
//            log.warn("请求URL参数中商户编码[{}]与Token中[{}]值不一致！", oldMerCode, merCode);
            String uriString = replaceParamMerCode(uri.toString(), merCode);
            try {
                uri = new URI(uriString);
            } catch (URISyntaxException e) {
                log.error("替换Url中参数是失败！", e);
            }
        }

        return uri;
    }

    /**
     * 替换URL参数中商户编码
     * @param urlStr URI
     * @param merCode 商户编码
     * @return 替换后的URI
     */
    private String replaceParamMerCode(String urlStr, String merCode) {
        StringBuilder sb = new StringBuilder();
        String[] merCodeSplit = urlStr.split("merCode=", 2);
        sb.append(merCodeSplit[0]);
        if (merCodeSplit.length == 2) {
            sb.append("merCode=").append(merCode);
            String[] andSplit = merCodeSplit[1].split("&", 2);
            if (andSplit.length == 2) {
                sb.append("&").append(andSplit[1]);
            }
        }
        return sb.toString();
    }
}