package cn.hydee.gateway.util;

import cn.hydee.gateway.config.KeyConfiguration;
import cn.hydee.gateway.domain.JWTInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Component
public class JwtTokenUtil {

    @Value("${jwt.expire}")
    private int expire;
    @Autowired
    private KeyConfiguration keyConfiguration;


    public String generateToken(JWTInfo jwtInfo) throws Exception {
        if (jwtInfo.getExprie() == 0) {
            return JWTHelper.generateToken(jwtInfo, keyConfiguration.getUserPriKey(), expire);
        }
        return JWTHelper.generateToken(jwtInfo, keyConfiguration.getUserPriKey(), jwtInfo.getExprie());
    }

    public JWTInfo getInfoFromToken(String token) throws Exception {
        return JWTHelper.getInfoFromToken(token, keyConfiguration.getUserPubKey());
    }

}
