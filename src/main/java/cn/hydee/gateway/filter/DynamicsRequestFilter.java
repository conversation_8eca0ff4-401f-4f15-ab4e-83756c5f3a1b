package cn.hydee.gateway.filter;

import com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.SneakyThrows;
import org.apache.http.HttpHost;
import org.apache.http.client.utils.URIUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;

import org.springframework.cloud.client.loadbalancer.LoadBalancerUriTools;
import org.springframework.cloud.gateway.config.GatewayLoadBalancerProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.support.DelegatingServiceInstance;
import org.springframework.cloud.gateway.support.NotFoundException;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;

import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/8/8 14:28
 */
public class DynamicsRequestFilter extends AbstractGatewayFilter {
    @Autowired
    private  GatewayLoadBalancerProperties properties;
    @Autowired
    @Lazy
    private NacosDiscoveryClient nacosDiscoveryClient;
    @SneakyThrows
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        HttpHeaders headers = exchange.getRequest().getHeaders();
        String route = headers.getFirst("route");
        if(StringUtils.isBlank(route)){
            return chain.filter(exchange);
        }
        URI uri = exchange.getAttribute(GATEWAY_REQUEST_URL_ATTR);
        //没有配置lb模式的，无法匹配应用，直接放行，网关层应该不存在ip对ip的替换
        if (uri == null || (!"lb".equals(uri.getScheme()))) {
            return chain.filter(exchange);
        }
        String appName = uri.getHost();
        JSONObject jsonObject = JSON.parseObject(route);
        Object o = jsonObject.get(appName);
        if(o==null || "".equals(o) || appName.equals(o)){
            return chain.filter(exchange);
        }
        String routeAddr = String.valueOf(o);

        //ip转发
        if(routeAddr.contains("http")){
            URI routeUri = new URI(routeAddr);
            HttpHost httpHost = new HttpHost(routeUri.getHost(),routeUri.getPort(),routeUri.getScheme());
            URI newUri = URIUtils.rewriteURI(uri, httpHost);
            exchange.getAttributes().put(GATEWAY_REQUEST_URL_ATTR, newUri);
        }else {
            String serviceId = routeAddr;
            List<ServiceInstance> serviceInstances = chooseStarted(serviceId);
            if(CollectionUtils.isEmpty(serviceInstances)){
                throw NotFoundException.create(properties.isUse404(), "Unable to find instance for " + uri.getHost());
            }
            ServiceInstance retrievedInstance = serviceInstances.get(0);
            URI requestUri = exchange.getRequest().getURI();

            String overrideScheme = retrievedInstance.isSecure() ? "https" : "http";
            DelegatingServiceInstance serviceInstance = new DelegatingServiceInstance(retrievedInstance,
                    overrideScheme);

            URI requestUrl = reconstructURI(serviceInstance, requestUri);
            exchange.getAttributes().put(GATEWAY_REQUEST_URL_ATTR, requestUrl);
        }
        return chain.filter(exchange);
    }

    private List<ServiceInstance> chooseStarted(String serviceId) {
        if (StringUtils.isBlank(serviceId)) {
            return new ArrayList<>(0);
        }
        List<ServiceInstance> serviceInstances = nacosDiscoveryClient.getInstances(serviceId);
        return serviceInstances;
    }


    protected URI reconstructURI(ServiceInstance serviceInstance, URI original) {
        return LoadBalancerUriTools.reconstructURI(serviceInstance, original);
    }

    @Override
    public int getOrder() {
        return FilterOrder.DynamicsRequestFilter;
    }
}