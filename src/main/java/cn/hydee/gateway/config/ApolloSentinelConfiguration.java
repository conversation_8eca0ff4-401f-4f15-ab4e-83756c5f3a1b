//package cn.hydee.gateway.config;
//
//import com.alibaba.csp.sentinel.adapter.gateway.common.api.ApiDefinition;
//import com.alibaba.csp.sentinel.adapter.gateway.common.api.ApiPathPredicateItem;
//import com.alibaba.csp.sentinel.adapter.gateway.common.api.ApiPredicateItem;
//import com.alibaba.csp.sentinel.adapter.gateway.common.api.GatewayApiDefinitionManager;
//import com.alibaba.csp.sentinel.adapter.gateway.common.rule.GatewayFlowRule;
//import com.alibaba.csp.sentinel.adapter.gateway.common.rule.GatewayRuleManager;
//import com.alibaba.csp.sentinel.adapter.gateway.sc.SentinelGatewayFilter;
//import com.alibaba.csp.sentinel.adapter.gateway.sc.callback.BlockRequestHandler;
//import com.alibaba.csp.sentinel.adapter.gateway.sc.callback.GatewayCallbackManager;
//import com.alibaba.csp.sentinel.adapter.gateway.sc.exception.SentinelGatewayBlockExceptionHandler;
//import com.alibaba.csp.sentinel.datasource.ReadableDataSource;
//import com.alibaba.csp.sentinel.datasource.apollo.ApolloDataSource;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.TypeReference;
//import com.alibaba.fastjson.parser.DefaultJSONParser;
//import com.alibaba.fastjson.parser.JSONToken;
//import com.alibaba.fastjson.parser.ParserConfig;
//import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
//import org.springframework.beans.factory.ObjectProvider;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.cloud.gateway.filter.GlobalFilter;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.Ordered;
//import org.springframework.core.annotation.Order;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.MediaType;
//import org.springframework.http.codec.ServerCodecConfigurer;
//import org.springframework.web.reactive.function.BodyInserters;
//import org.springframework.web.reactive.function.server.ServerResponse;
//import org.springframework.web.reactive.result.view.ViewResolver;
//
//import javax.annotation.PostConstruct;
//import java.lang.reflect.Type;
//import java.util.*;
//
//@Configuration
//@ConfigurationProperties(prefix = "sentinel.apollo")
//@ConditionalOnProperty(prefix = "sentinel.apollo", name = "enable", matchIfMissing = true)
//public class ApolloSentinelConfiguration {
//    private static final String NAMESPACE_NAM = "application.yml";
//    private static final String DEFAULT_VALUE = "[]";
//    private static final String FLOW_RULES = "flowRules";
//    private static final String API_DEFINITIONS = "apiDefinitions";
//
//    @Value("${sentinel.tip-code:10008}")
//    private Integer responseErrorCode;
//
//    @Value("${sentinel.tip-message:当前页面太火爆了，请刷新后重试}")
//    private String responseErrorMessage;
//
//    private final List<ViewResolver> viewResolvers;
//    private final ServerCodecConfigurer serverCodecConfigurer;
//
//    public ApolloSentinelConfiguration(ObjectProvider<List<ViewResolver>> viewResolversProvider,
//                                       ServerCodecConfigurer serverCodecConfigurer) {
//        this.viewResolvers = viewResolversProvider.getIfAvailable(Collections::emptyList);
//        this.serverCodecConfigurer = serverCodecConfigurer;
//    }
//
//    @Bean
//    @Order(Ordered.HIGHEST_PRECEDENCE)
//    public SentinelGatewayBlockExceptionHandler sentinelGatewayBlockExceptionHandler() {
//        return new SentinelGatewayBlockExceptionHandler(viewResolvers, serverCodecConfigurer);
//    }
//
//    @Bean
//    @Order(Ordered.HIGHEST_PRECEDENCE)
//    public GlobalFilter sentinelGatewayFilter() {
//        return new SentinelGatewayFilter();
//    }
//
//    @PostConstruct
//    public void doInit() {
//        BlockRequestHandler blockRequestHandler = (serverWebExchange, throwable) -> {
//            Map<String, Object> paramMap = new HashMap<>();
//            paramMap.put("code", responseErrorCode);
//            paramMap.put("msg", responseErrorMessage);
//            paramMap.put("data", null);
//            paramMap.put("timestamp", System.currentTimeMillis());
//            return ServerResponse.status(HttpStatus.OK)
//                    .contentType(MediaType.APPLICATION_JSON)
//                    .body(BodyInserters.fromObject(paramMap));
//        };
//        GatewayCallbackManager.setBlockHandler(blockRequestHandler);
//
//        loadRulesAndApiDefinitions();
//    }
//
//    private void loadRulesAndApiDefinitions() {
//        // 新增自定义反序列化接口
//        ParserConfig parserConfig = new ParserConfig();
//        parserConfig.putDeserializer(ApiPredicateItem.class, new ApiPredicateItemDeserializer());
//
//        // 注册流控规则
//        ReadableDataSource<String, Set<GatewayFlowRule>> flowRuleDataSource = new ApolloDataSource<>(NAMESPACE_NAM, FLOW_RULES, DEFAULT_VALUE,
//                source -> JSON.parseObject(source, new TypeReference<Set<GatewayFlowRule>>() {
//                }));
//        GatewayRuleManager.register2Property(flowRuleDataSource.getProperty());
//
//        // 配置API分组
//        ReadableDataSource<String, Set<ApiDefinition>> apiDefinitionDataSource = new ApolloDataSource<>(NAMESPACE_NAM, API_DEFINITIONS, DEFAULT_VALUE,
//                source -> JSON.parseObject(source, (new TypeReference<Set<ApiDefinition>>() {
//                }).getType(), parserConfig));
//        GatewayApiDefinitionManager.register2Property(apiDefinitionDataSource.getProperty());
//    }
//}
//
//class ApiPredicateItemDeserializer implements ObjectDeserializer {
//
//    @SuppressWarnings("unchecked")
//    @Override
//    public ApiPredicateItem deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
//        return parser.parseObject(ApiPathPredicateItem.class);
//    }
//
//    @Override
//    public int getFastMatchToken() {
//        return JSONToken.LBRACE;
//    }
//}