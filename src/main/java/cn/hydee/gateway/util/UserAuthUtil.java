package cn.hydee.gateway.util;

import cn.hydee.gateway.config.KeyConfiguration;
import cn.hydee.gateway.config.UserAuthConfig;
import cn.hydee.gateway.domain.JWTInfo;
import cn.hydee.gateway.exception.UserTokenException;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.SignatureException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UserAuthUtil {

    @Autowired
    private KeyConfiguration keyConfiguration;

    public JWTInfo getInfoFromToken(String token) throws Exception {
        try {
            return JWTHelper.getInfoFromToken(token, keyConfiguration.getUserPubKey());
        }catch (ExpiredJwtException ex){
            throw new UserTokenException("User token expired!");
        }catch (SignatureException ex){
            throw new UserTokenException("User token signature error!");
        }catch (IllegalArgumentException ex){
            throw new UserTokenException("User token is null or empty!");
        }
    }
}
