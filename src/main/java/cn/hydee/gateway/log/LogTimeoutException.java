package cn.hydee.gateway.log;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.error.DefaultErrorAttributes;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年01月19日 10:04
 * @email: <EMAIL>
 */
@Slf4j
@Component
public class LogTimeoutException extends DefaultErrorAttributes {
    @Override
    public void storeErrorInformation(Throwable error, ServerWebExchange exchange) {
        try {
            HttpStatus httpStatus = determineHttpStatus(error);
            if (HttpStatus.GATEWAY_TIMEOUT.equals(httpStatus)) {
                String requestUri = exchange.getRequest().getPath().pathWithinApplication().value();
                log.warn("business-gateway timeout url:{}", requestUri);
            }
        } catch (Exception ignored) {
            log.warn("business gateway logTimeout Exception", ignored); // 兜底打印warn日志,不打印error
        }

        super.storeErrorInformation(error, exchange);
    }


    private HttpStatus determineHttpStatus(Throwable error) {
        if (error instanceof ResponseStatusException) {
            return ((ResponseStatusException) error).getStatus();
        } else {
            ResponseStatus responseStatus = (ResponseStatus) AnnotatedElementUtils.findMergedAnnotation(error.getClass(), ResponseStatus.class);
            return responseStatus != null ? responseStatus.code() : HttpStatus.INTERNAL_SERVER_ERROR;
        }
    }

}