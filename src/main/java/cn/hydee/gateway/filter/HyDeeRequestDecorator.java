package cn.hydee.gateway.filter;

import static reactor.core.scheduler.Schedulers.single;

import cn.hydee.gateway.util.IpUtils;
import io.netty.buffer.ByteBufAllocator;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.core.io.buffer.NettyDataBufferFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import reactor.core.publisher.Flux;

/**
 * 网络请求装饰器
 * <AUTHOR>
 * @version 1.0
 * @date 2019/9/24 15:05
 */
@Slf4j
public class HyDeeRequestDecorator extends ServerHttpRequestDecorator {


    private Boolean hyDeeRequestDecoratorReleaseOnOff;

    public HyDeeRequestDecorator(ServerHttpRequest request,
        Boolean hyDeeRequestDecoratorReleaseOnOff) {
        super(request);
        this.hyDeeRequestDecoratorReleaseOnOff = hyDeeRequestDecoratorReleaseOnOff;
    }


    @Override
    public Flux<DataBuffer> getBody() {
            Flux<DataBuffer> flux = super.getBody();
            return flux
                    .publishOn(single())
                    .map(this::cache);
    }

    private DataBuffer cache(DataBuffer buffer) {
        InputStream dataBuffer = buffer.asInputStream();
        try {
            byte [] bytes = IOUtils.toByteArray(dataBuffer);
            String content = new String(bytes, StandardCharsets.UTF_8);
            trace(getDelegate(),content);
            return stringBuffer(content);
        } catch (IOException e) {
            log.error("缓存请求数据错误",e);
        } finally {
            if (hyDeeRequestDecoratorReleaseOnOff) {
                DataBufferUtils.release(buffer);
            }
            try {
                dataBuffer.close();
            } catch (IOException e) {
//                log.warn("缓存请求数据释放失败",e);
            }
        }
        return stringBuffer("");
    }

    private void trace(ServerHttpRequest request, String bodyStr) {
        String requestUri = request.getPath().pathWithinApplication().value();
        String ip = IpUtils.getIpAddress(request);
//        log.info("用户登录请求：{}，信息：{}，IP：{}",requestUri,bodyStr,ip);
    }

    private DataBuffer stringBuffer(String value) {
        byte[] bytes = value.getBytes(StandardCharsets.UTF_8);
        NettyDataBufferFactory nettyDataBufferFactory = new NettyDataBufferFactory(ByteBufAllocator.DEFAULT);
        DataBuffer buffer = nettyDataBufferFactory.allocateBuffer(bytes.length);
        buffer.write(bytes);
        return buffer;
    }
}
