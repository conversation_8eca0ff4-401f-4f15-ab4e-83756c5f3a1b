package cn.hydee.gateway.filter;

import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_ORIGINAL_REQUEST_URL_ATTR;

import cn.hydee.gateway.config.ExcludeUrlConfig;
import cn.hydee.gateway.config.UserAuthConfig;
import cn.hydee.gateway.constants.CommonConstants;
import cn.hydee.gateway.domain.JWTInfo;
import cn.hydee.gateway.domain.ReturnData;
import cn.hydee.gateway.domain.TokenForbiddenResponse;
import cn.hydee.gateway.domain.UrlForbiddenResponse;
import cn.hydee.gateway.util.Const;
import cn.hydee.gateway.util.UserAuthUtil;
import com.alibaba.fastjson.JSONObject;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ReactiveHashOperations;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * 权限校验过滤器
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(ExcludeUrlConfig.class)
public class AccessGatewayFilter extends AbstractGatewayFilter {

    private static final String URL_SPLIT = "/";
    private static final String PATH_VARIABLE_SPLIT = "/{";
    protected final static String HEAD_USER_ID_KEY = "userId";
    private final static String HEAD_USER_NAME_KEY = "userName";
    /**
     * 用户中文名
     */
    private final static String HEAD_USER_ZH_NAME_KEY = "userZhName";
    protected final static String HEAD_USER_MERCODE = "merCode";
    /**
     * 员工编码标识
     */
    public static final String HEAD_EMP_CODE = "empCode";
    /**
     * 账号类型标识
     */
    public static final String HEAD_ACC_TYPE = "accType";
    private static final String GATE_WAY_PREFIX = "/api";
    //针对哪些商户放行
    @Value("${token.resolver.igrone.mercodes:'SPHYDEE,hydee'}")
    private String HEAD_PASS_MERCODES = "SPHYDEE";

    @Autowired
    private ExcludeUrlConfig excludeUrlConfig;

    @Autowired
    private UserAuthConfig userAuthConfig;

    @Autowired
    private UserAuthUtil userAuthUtil;

    @Autowired
    @Deprecated
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    @Deprecated
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;


    @Override
    public Mono<Void> filter(ServerWebExchange serverWebExchange, GatewayFilterChain gatewayFilterChain) {
        //如果不重新校验,则直接放行
        if (Boolean.FALSE.equals(serverWebExchange.getAttributes().get(YxtLoginAccessGatewayFilterFactory.RE_CHECK_TOKEN))) {
            return gatewayFilterChain.filter(serverWebExchange);
        }

        //1. 处理requestUri
        serverWebExchange.getAttributes()
            .computeIfAbsent(GATEWAY_ORIGINAL_REQUEST_URL_ATTR, s -> new LinkedHashSet<>());
        LinkedHashSet requiredAttribute = serverWebExchange.getRequiredAttribute(GATEWAY_ORIGINAL_REQUEST_URL_ATTR);
        ServerHttpRequest request = serverWebExchange.getRequest();
        String requestUri = request.getPath().pathWithinApplication().value();
        String method = request.getMethodValue();
        Iterator<URI> iterator = requiredAttribute.iterator();
        while (iterator.hasNext()) {
            URI next = iterator.next();
            if (next.getPath().startsWith(GATE_WAY_PREFIX)) {
                requestUri = next.getPath().substring(GATE_WAY_PREFIX.length());
            }
        }

        // 2. 从Token中解析用户信息
        ServerHttpRequest.Builder mutate = request.mutate();
        JWTInfo user = getJWTUser(request, mutate);

        // 3. 不进行登录拦截的地址
        if (excludeUrlConfig.isStartWith(requestUri) || excludeUrlConfig.isEndWith(requestUri)) {
            return permit(serverWebExchange, gatewayFilterChain);
        }

        // 4.需要登录，判断用户toke有效
        if (user == null) {
            return forbiddenResponse(serverWebExchange,
                new TokenForbiddenResponse("User Token Forbidden or Expired!"));
        }
        if (!stringRedisTemplate.opsForSet().isMember(Const.EFFECTIVE_LOGIN_USER_ID, user.getUserId())) {
            return forbiddenResponse(serverWebExchange,
                new TokenForbiddenResponse("User Token Forbidden or Expired!"));
        }

        // 5.判断链接是否收费，不收费放行
        String matchPath = matchNeedPayPath(method, requestUri);
        if (StringUtils.isEmpty(matchPath)) {
            return permit(serverWebExchange, gatewayFilterChain);
        }

        // 6.判断资源是否有效
        String userId = user.getUserId();
        if (!checkUserPermission(userId, matchPath)) {
            return forbiddenResponse(serverWebExchange,
                new UrlForbiddenResponse("User Forbidden!Does not has Permission!"));
        }

        // 7.重新构建Request,返回
        ServerHttpRequest build = mutate.build();
        return gatewayFilterChain.filter(serverWebExchange.mutate().request(build).build());

    }

    private Mono<Void> permit(ServerWebExchange serverWebExchange, GatewayFilterChain gatewayFilterChain) {
        ServerHttpRequest request = serverWebExchange.getRequest();
        String requestUri = request.getPath().pathWithinApplication().value();
        if (excludeUrlConfig.isLogEndWith(requestUri) && Const.METHOD_POST.equals(request.getMethodValue())) {
            // 打印用户登录信息,从请求里获取Post请求体
            ServerHttpRequestDecorator decorator = new HyDeeRequestDecorator(request);
            return gatewayFilterChain.filter(serverWebExchange.mutate().request(decorator).build());
        }
        return gatewayFilterChain.filter(serverWebExchange.mutate().request(request).build());
    }

    @Override
    public int getOrder() {
        return FilterOrder.AccessGatewayFilter;
    }

    /**
     * 校验token是否过期，是否有url，是否过期
     *
     * @param matchPath 匹配上的路径
     * @return true 有权限，false-无权限
     */
    private boolean checkUserPermission(String userId, String matchPath) {
        HashOperations<String, String, Date> hashOperations = redisTemplate.opsForHash();
        Date date = hashOperations.get(CommonConstants.REDIS_PERMISSION_KEY + userId, matchPath);
        if (date == null) {
            return false;
        }
        return System.currentTimeMillis() <= date.getTime();
    }

    /**
     * 网关抛异常
     *
     * @param body 返回的数据
     */
    private Mono<Void> forbiddenResponse(ServerWebExchange serverWebExchange, ReturnData body) {
        serverWebExchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
        byte[] bytes = JSONObject.toJSONString(body).getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = serverWebExchange.getResponse().bufferFactory().wrap(bytes);
        return serverWebExchange.getResponse().writeWith(Flux.just(buffer));
    }

    /**
     * 返回token中用户信息
     *
     * @param request 请求
     * @param ctx     请求构建者
     * @return token 中包含的用户信息
     */
    private JWTInfo getJWTUser(ServerHttpRequest request, ServerHttpRequest.Builder ctx) {
        try {
            String authToken = request.getHeaders().getFirst(userAuthConfig.getTokenHeader());
            if (StringUtils.isBlank(authToken)) {
                authToken = request.getQueryParams().getFirst("token");
            }
            if (authToken == null) {
                return null;
            }
            JWTInfo info = userAuthUtil.getInfoFromToken(authToken);
            if (info == null) {
                return null;
            }
            String multiLogin = info.getMultiLogin();
            String userId = info.getUserId();
            // 判断用户是否多端登录，如果非多端登录，需要验证当前用户是否已经登录的缓存key是否和当前token一致
            if (Objects.equals(multiLogin, Boolean.FALSE.toString())) {
                Object loginTokenCache = stringRedisTemplate.opsForValue()
                    .get(Const.REDIS_OAUTH_LOGIN_TOKEN_KEY + userId);
                if (loginTokenCache == null) {
                    //                log.warn("用户{}未登录", userId);
                    return null;
                }
                if (!Objects.equals(authToken, loginTokenCache.toString())) {
                    //                log.warn("用户{}已在其他地方登录, 当前token已失效", userId);
                    return null;
                }
            }
            ctx.headers(httpHeaders -> {
                httpHeaders.set(HEAD_USER_ID_KEY, userId);
                httpHeaders.set(HEAD_USER_NAME_KEY, info.getUserName());
                httpHeaders.set(HEAD_EMP_CODE, info.getEmpCode());
                try {
                    httpHeaders.set(HEAD_USER_ZH_NAME_KEY,
                        URLEncoder.encode(info.getZhName(),
                            String.valueOf(StandardCharsets.UTF_8)));
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
                httpHeaders.set(HEAD_ACC_TYPE,
                    info.getAccType() == null ? "" : String.valueOf(info.getAccType()));
            });
            String headerMerCode = request.getHeaders().getFirst(HEAD_USER_MERCODE);
            String tokenMerCodee = info.getMerCode();
            log.debug("打印参数据{},{}", headerMerCode, tokenMerCodee);
            if (!ObjectUtils.isEmpty(headerMerCode) && !ObjectUtils.isEmpty(tokenMerCodee)
                && !headerMerCode.equals(
                tokenMerCodee)) {
                if (!HEAD_PASS_MERCODES.contains(tokenMerCodee)) {
                    log.debug("商户token信息不匹配{},{}", headerMerCode, tokenMerCodee);
                    return null;
                }
            }
            //登录强制重置header里面的商户号
            if (!ObjectUtils.isEmpty(tokenMerCodee) && !HEAD_PASS_MERCODES.contains(
                tokenMerCodee)) {
                List<String> merCodes = new ArrayList<>();
                merCodes.add(tokenMerCodee);
                ctx.headers(httpHeaders -> {
                    httpHeaders.put(HEAD_USER_MERCODE, merCodes);
                });
            }
            setZhNameToRedis(info);
            return info;
        } catch (Exception e) {
            return null;
        }
    }

    public static void main(String[] args) {

    }

    private void setZhNameToRedis(JWTInfo info) {
        ReactiveHashOperations<String, String, String> hashOperations = reactiveRedisTemplate.opsForHash();
        hashOperations.put(Const.USER_ZH_NAME_KEY, info.getUserName(),
                info.getZhName())
            .subscribeOn(Schedulers.boundedElastic()) // 在独立线程池执行
            .subscribe(
                result -> {
                    if (!result) {
                        log.warn("用户中文名缓存失败: {}", info.getUserName());
                    }
                },
                error -> log.warn(String.format("用户中文名【%s】缓存异常", info.getUserName()),
                    error)
            ) //在响应式环境中,订阅才会执行
        ;
    }

    /**
     * 匹配当前资源是否是需要支付的资源
     *
     * @param path 访问路径
     * @return 找到匹配的资源
     * <AUTHOR>
     * @date 15:57 2019/7/30
     **/
    private String matchNeedPayPath(String method, String path) {
        SetOperations<String, Object> opsForSet = redisTemplate.opsForSet();
        Set<Object> members = opsForSet.members(Const.NEED_PAY_RES_KEY);
        if (members == null) {
            return null;
        }
        for (Object value : members) {
            String expect = (String) value;
            if (isRight(expect, method + path)) {
                return expect;
            }
        }
        return null;
    }

    /**
     * 实际访问路径是否与期望路径匹配
     *
     * @param respect 期望路径
     * @param actual  实际路径
     * @return boolean true-匹配，false-不匹配
     * <AUTHOR>
     * @date 15:44 2019/8/15
     **/
    private boolean isRight(String respect, String actual) {
        if (respect.contains(PATH_VARIABLE_SPLIT)) {
            return respect.split(URL_SPLIT).length == actual.split(URL_SPLIT).length && actual.startsWith(
                respect.substring(0, respect.indexOf(PATH_VARIABLE_SPLIT)));
        } else {
            return actual.equals(respect);
        }
    }
}
