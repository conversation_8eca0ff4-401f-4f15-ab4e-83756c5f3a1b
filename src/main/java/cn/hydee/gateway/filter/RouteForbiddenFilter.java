package cn.hydee.gateway.filter;

import cn.hydee.gateway.config.RouteForbiddenConfig;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年05月14日 14:04
 * @email: <EMAIL>
 */
@Component
public class RouteForbiddenFilter implements GlobalFilter, Ordered {

  @Resource
  private RouteForbiddenConfig routeForbiddenConfig;

  @Override
  public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
    String path = exchange.getRequest().getURI().getPath();
    if (StringUtils.isNotEmpty(path) && routeForbiddenConfig.containUrl(path)) {
      exchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
      return exchange.getResponse().setComplete();
    }
    return chain.filter(exchange);
  }

  /**
   * 得到权重，order越小，优先级越高
   *
   * @return int
   */
  @Override
  public int getOrder() {
    return FilterOrder.RouteForbiddenFilter;
  }

}
