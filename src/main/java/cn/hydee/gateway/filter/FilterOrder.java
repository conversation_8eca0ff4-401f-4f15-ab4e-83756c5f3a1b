package cn.hydee.gateway.filter;

/**
 * 过滤器顺序,统一放到一起,好管理
 *
 * <AUTHOR> (moatkon)
 * @date 2024年05月14日 13:36
 * @email: <EMAIL>
 */
public interface FilterOrder {

  int RouteForbiddenFilter = Integer.MIN_VALUE + 2;
  int BodygzipFilter = Integer.MIN_VALUE + 3;
  int AccessGatewayFilter = Integer.MIN_VALUE + 5;
  int IdempotentFilter = Integer.MIN_VALUE + 10;
  int RewriteMerCodeFilter = Integer.MIN_VALUE + 20;//暂时没有使用
  int ModifyBodyGatewayFilter = Integer.MIN_VALUE + 30;
  int DynamicsRequestFilter = 10149;//早于GreyReactiveLoadBalancerClientFilter

  //保证鉴权服务拦截器在AccessGatewayFilter之前
  int YxtLoginAccessGatewayFilter = Integer.MIN_VALUE + 4;
}
