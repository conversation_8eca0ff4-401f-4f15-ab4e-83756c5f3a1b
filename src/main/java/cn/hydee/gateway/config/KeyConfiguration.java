package cn.hydee.gateway.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.bouncycastle.util.encoders.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "jwt")
@Getter
@Setter
@ToString
public class KeyConfiguration {

    @Value("${jwt.rsa-secret}")
    private String userSecret;
    // 暂定删除
    private byte[] servicePubKey;

    private String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1CU7En9Xtl6/nnJ4tTjz6U08wFy5vMBA\n" +
        "1UdGA5m22FhRt4B0exvhYBE1XyZp0jm4MQ071t5Re64VN8Gsx1AGujx0PW7leOSQmw1Teb3pgucT\n" +
        "7ENn5bVmG52pe3//aWhgZUYe+8epYOK89PhmqwD7t80FZKp+T10QDeQizWx6ZEQZMApzlTj/GX64\n" +
        "FC2Qld42B/Dcg+crjX0cI+3f9YIwQmYR9/6DifxybAs37uHSuzneqVc9Q6jJ/glYqsOuikxrV1eO\n" +
        "gW8/gO+b6OVVKFRxgjUE4GEolkP6DdCKD/+9nrtITtHTCgka1LcDOo0PEAlu8B9UCgtB44HiQ6OT\n" +
        "Ko074QIDAQAB";

    private String privateKey = "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDUJTsSf1e2Xr+ecni1OPPpTTzA\n" +
        "XLm8wEDVR0YDmbbYWFG3gHR7G+FgETVfJmnSObgxDTvW3lF7rhU3wazHUAa6PHQ9buV45JCbDVN5\n" +
        "vemC5xPsQ2fltWYbnal7f/9paGBlRh77x6lg4rz0+GarAPu3zQVkqn5PXRAN5CLNbHpkRBkwCnOV\n" +
        "OP8ZfrgULZCV3jYH8NyD5yuNfRwj7d/1gjBCZhH3/oOJ/HJsCzfu4dK7Od6pVz1DqMn+CViqw66K\n" +
        "TGtXV46Bbz+A75vo5VUoVHGCNQTgYSiWQ/oN0IoP/72eu0hO0dMKCRrUtwM6jQ8QCW7wH1QKC0Hj\n" +
        "geJDo5MqjTvhAgMBAAECggEAdewACwcn7WJ7ZwrAWBPBWQSq6U5hnC7NmwAURfoSre1h9vt7Q9c6\n" +
        "jtjh7vL+TXHyFaoCgixv5KzedHC+psewBaOHBYL4IRwD2Xwt03UiMLZb16+cOA5+JImd5N1ATwpK\n" +
        "0uCIZnIUj5tZZ14QS6uvP/rBrbQro9faDa6rPb63xbUljEWS/vwABg3exk6ZE83Map8/t4yA6jXr\n" +
        "DlMDHDSTgmxMPPOL3V4xA9Miup48HfTHZSNQYLQYdi4spSNa/ShcXQzU89uw+L6B31gE1X+sni+O\n" +
        "+/2/6pG/db46FGMjyf4lN8n4CWFBZpNwn9Mx6YIgciXho8c79atvT45w6wQYNQKBgQDwMX+mLfmf\n" +
        "/k6/rRr7aaAtomtfCIs8ftH2x953XDY7FyGm22nwTJQyStrk6lsgSTnEFqiBaSVo+gLYsOayz5hp\n" +
        "DSlCR2ON5nmXjcsKN81TjZasaqJtFHnQFEguXMjNO3HSht+CQy1PqiR7g/+WG5K5CtZOLFeZJWda\n" +
        "WsUIEOn4JwKBgQDiGzaHPPbB9bYPjK4xrTA48+X1oVVd84B86XF+WNRwLt+nxSeGl1mhgf1dbUwl\n" +
        "mfkJ8JWUSEcBdKQ6/HTQ1KVPoU4GvDrtB9+u53Q95nCg7PNP8FxFy9xLW7pvvOAtigBNvyA5/6bW\n" +
        "P+iZlPLzFCD2gNrV1E6dTX6LZv3BbrtotwKBgD1BwnZVP1aIevjzYvA/jfRVeeCwwyHYjh/L9Xan\n" +
        "c8U6LwDo8GmyfhEdehnrkvX29t6FkNAASuQs7tDovM1ZmFsiWiVnI4KrBtOZ+/BNLvZiI80xHN+I\n" +
        "21Av/ACm9ScyQ+MXNqV4EHRCxi8iCOr1eUPr+Z/SOzz5hxRKFfnqpmD9AoGAbwsiWoSsQ1GjGjvj\n" +
        "CO6fcjkGRP346O0/uxkC7iQ+IOJEYSbIZKdME6+9ePbGoOjfj+wh6fMdPC/GwFlgHXdxwIvdwB0N\n" +
        "a3+kcUempQfZZI86Z5YKGxUlmbTw+RNvna3avFQTRaiyjSJR36txLcRC9A0QbFeaTFLUxS3NjgQN\n" +
        "8j0CgYAnHBWQuZfbC6FB16bIhhWCYUGc9P2lZZ6RwTaumJE3a3hIPOOx8e3r8uMIFJBruEOfigbu\n" +
        "ISHJDRFSiaqKUodIRqywU4flcwgTAIQeu2IFJ28wze9UkezLqbdSdyoKAUeCvN2ZblyLOjibyoqK\n" +
        "Btbp2PU/Uh5bfUL85c0sI+QR/A==";

    public byte[] getUserPubKey() {
        return Base64.decode(publicKey);
    }

    public byte[] getUserPriKey() {
        return Base64.decode(privateKey);
    }
}
