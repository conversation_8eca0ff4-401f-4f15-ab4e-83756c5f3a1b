package cn.hydee.gateway.config;

import cn.hydee.gateway.filter.DynamicsRequestFilter;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;

@Configuration
@ConditionalOnProperty(value = "dynamic.enable", havingValue = "true")
public class DynamicsConfiguration {

    @Bean
    public DynamicsRequestFilter dynamicsRequestFilter() {
        return new DynamicsRequestFilter();
    }

}
